"""
Bot de WhatsApp con Gemini - Versión con módulo central
Este script maneja la interfaz de WhatsApp Web y utiliza el módulo bot_core para la lógica principal.
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
import time
import random
import emoji
import os
import pathlib
import re
from datetime import datetime

# Importar el módulo central del bot
from src.bot.core import (
    inicializar_bot,
    procesar_mensaje_y_generar_respuesta,
    limpiar_emojis_para_selenium,
    modo_silencioso,
    modo_debug
)

# Importar el módulo de temperatura
from src.utils.temperature import (
    establecer_temperatura,
    obtener_temperatura
)

# Importar configuración centralizada
from src.config import config

# Variables globales para seguimiento de mensajes
ultimo_mensaje_procesado = ""
tiempo_ultimo_mensaje = 0
chat_actual = None

def iniciar_whatsapp():
    """Inicia el navegador y carga WhatsApp Web."""
    try:
        print("🔄 Iniciando navegador Chrome...")
        # Configurar opciones de Chrome desde la configuración centralizada
        chrome_options = Options()
        for option in config.CHROME_OPTIONS:
            chrome_options.add_argument(option)

        # Configurar directorio de usuario para mantener la sesión
        chrome_options.add_argument(f"--user-data-dir={config.USER_DATA_DIR}")

        # Iniciar Chrome con las opciones configuradas
        driver = webdriver.Chrome(options=chrome_options)

        # Abrir WhatsApp Web
        driver.get(config.WHATSAPP_WEB_URL)

        # Verificar si necesitamos escanear el código QR
        print("⏳ Verificando si es necesario escanear el código QR...")

        # Esperar un momento para que la página cargue
        time.sleep(config.TIEMPO_CARGA_WHATSAPP)

        # Verificar si hay un código QR visible (lo que indicaría que necesitamos iniciar sesión)
        qr_code_visible = False
        try:
            qr_code = driver.find_element(By.XPATH, "//canvas[contains(@aria-label, 'Scan me!')]")
            qr_code_visible = qr_code.is_displayed()
        except:
            qr_code_visible = False

        if qr_code_visible:
            print("📱 Por favor, escanea el código QR con tu teléfono.")
            input("✅ Después de escanear, presiona Enter para continuar...")
            print("✅ Código QR escaneado correctamente.")
        else:
            print("✅ Sesión de WhatsApp Web cargada correctamente. No es necesario escanear el código QR.")

        return driver

    except Exception as e:
        print(f"❌ Error al iniciar Chrome: {e}")
        return None

# 📥 Leer el último mensaje recibido del chat abierto
def leer_ultimo_mensaje(driver):
    """Lee el último mensaje recibido del chat abierto."""
    global ultimo_mensaje_procesado, tiempo_ultimo_mensaje, chat_actual
    try:
        # Usar un identificador fijo para el chat actual si no podemos detectar el nombre
        chat_id = "Chat_Actual"

        # Intentar obtener el nombre del chat actual (varios selectores posibles)
        try:
            # Intentar diferentes selectores para encontrar el nombre del chat
            selectores = [
                "//header//span[@dir='auto' and @title]",
                "//header//div[contains(@class, 'chat-title')]//span",
                "//header//div[contains(@class, 'title')]//span",
                "//header//div[contains(@class, 'conversation-title')]"
            ]

            for selector in selectores:
                try:
                    elementos = driver.find_elements(By.XPATH, selector)
                    if elementos and len(elementos) > 0:
                        for elem in elementos:
                            nombre = elem.text.strip() or elem.get_attribute("title")
                            if nombre and len(nombre) > 0:
                                chat_actual = nombre
                                print(f"✅ Nombre del chat detectado: {chat_actual}")
                                break
                        if chat_actual:
                            break
                except:
                    continue

            # Si no se pudo detectar el nombre, usar el ID fijo
            if not chat_actual:
                chat_actual = chat_id
                print("⚠️ No se pudo detectar el nombre del chat, usando ID genérico")

        except Exception as e:
            chat_actual = chat_id
            print(f"⚠️ Error al detectar el nombre del chat: {e}")

        # Leer el último mensaje recibido
        mensajes_recibidos = driver.find_elements(By.XPATH, "//*[contains(@class, 'message-in')]//*[contains(@class, 'selectable-text')]")

        if mensajes_recibidos and len(mensajes_recibidos) > 0:
            texto = mensajes_recibidos[-1].text.strip()

            if texto != ultimo_mensaje_procesado:
                tiempo_actual = time.time()
                if tiempo_actual - tiempo_ultimo_mensaje > 2:
                    print(f"📨 Mensaje recibido de {chat_actual}: {texto}")

                    # Actualizar variables de seguimiento
                    ultimo_mensaje_procesado = texto
                    tiempo_ultimo_mensaje = tiempo_actual

                    return texto
    except Exception as e:
        print("❌ Error al leer mensaje:", e)
    return ""

# 📤 Enviar respuesta al chat abierto
def enviar_respuesta(driver, texto):
    """Envía una respuesta al chat abierto."""
    # Si el texto es None (modo silencioso), no hacer nada
    if texto is None:
        return

    try:
        # Procesar emojis para que sean compatibles con Selenium
        texto_procesado = limpiar_emojis_para_selenium(texto)

        # Asegurarse de que los emojis se muestren correctamente
        # Convertir cualquier emoji en formato :emoji: a su representación gráfica
        texto_procesado = emoji.emojize(texto_procesado, language='alias')

        # Limpiar el texto para evitar caracteres no soportados por ChromeDriver
        # ChromeDriver solo soporta caracteres en el BMP (Basic Multilingual Plane)
        texto_limpio = ""
        for c in texto_procesado:
            # Filtrar caracteres que no están en el BMP (código > 0xFFFF)
            if ord(c) <= 0xFFFF:
                texto_limpio += c
            else:
                # Reemplazar caracteres especiales con alternativas más apropiadas
                # en lugar de siempre usar "?"
                texto_limpio += " "

        # Si el texto se modificó, mostrar advertencia
        if texto_limpio != texto and modo_debug:
            print("⚠️ Se han modificado caracteres especiales del mensaje")

        # Buscar la caja de texto usando diferentes selectores
        selectores_caja = [
            "//footer//div[@contenteditable='true' and @data-tab]",
            "//footer//div[@contenteditable='true']",
            "//div[@contenteditable='true']"
        ]

        caja = None
        for selector in selectores_caja:
            try:
                elementos = driver.find_elements(By.XPATH, selector)
                if elementos and len(elementos) > 0:
                    caja = elementos[0]
                    break
            except:
                continue

        if not caja:
            raise Exception("No se pudo encontrar la caja de texto")

        # Enviar el mensaje
        caja.click()

        # Enviar el texto carácter por carácter para mayor seguridad
        for c in texto_limpio:
            caja.send_keys(c)

        # Enviar Enter para mandar el mensaje
        caja.send_keys("\n")

        print(f"📤 Respuesta enviada a {chat_actual}: {texto_limpio}")
    except Exception as e:
        print("❌ No se pudo escribir el mensaje:", e)

        # Intentar con un mensaje más simple en caso de error
        try:
            mensajes_alternativos = [
                "hola que tal va todo",
                "perdon que me decias",
                "como va tu dia",
                "que tal todo",
                "oye que planes tienes",
                "todo bien por ahi?",
                "que me cuentas de nuevo",
                "que pasa tio",
                "ey que tal",
                "que hay",
                "buenas que tal",
                "hey como va",
                "que tal el dia",
                "que me cuentas",
                "todo bien?",
                "que tal el finde"
            ]
            mensaje_alternativo = random.choice(mensajes_alternativos)

            # Buscar la caja de texto de nuevo
            caja = driver.find_element(By.XPATH, "//div[@contenteditable='true']")
            caja.click()
            caja.send_keys(mensaje_alternativo)
            caja.send_keys("\n")

            print(f"📤 Mensaje alternativo enviado: {mensaje_alternativo}")
        except:
            print("❌ No se pudo enviar ni siquiera un mensaje alternativo")

def ejecutar_bot():
    """Función principal para ejecutar el bot de WhatsApp."""
    # Inicializar el bot con el módulo central
    inicializar_bot(config.HISTORIAL_ARCHIVO)

    # Iniciar WhatsApp Web
    driver = iniciar_whatsapp()
    if not driver:
        print("❌ No se pudo iniciar WhatsApp Web.")
        return

    # Loop principal
    print("🤖 Bot activo. Esperando mensajes...")
    print("📋 Comandos disponibles: /ayuda, /status, /borrar, /silencio, /hablar, /debug, /info, /vocab, /vocab_on, /vocab_off, /temp, /temp [0-100]")
    print("📚 Aprendizaje de vocabulario activado. El bot aprenderá palabras y frases de las conversaciones.")

    # Mostrar la temperatura actual
    temp_actual = obtener_temperatura()
    temp_porcentaje = int(temp_actual * 100)
    print(f"🌡️ Temperatura de IA actual: {temp_porcentaje}%")

    try:
        while True:
            # Leer el último mensaje
            mensaje = leer_ultimo_mensaje(driver)

            # Si hay un mensaje nuevo, procesarlo
            if mensaje:
                # Procesar el mensaje y generar una respuesta usando el módulo central
                respuesta = procesar_mensaje_y_generar_respuesta(mensaje, chat_actual, config.HISTORIAL_ARCHIVO)

                # Enviar la respuesta (puede ser una lista de respuestas o una sola)
                if respuesta:
                    if isinstance(respuesta, list):
                        # Si son múltiples mensajes, enviarlos secuencialmente con pausas
                        print(f"📤 Enviando {len(respuesta)} mensajes secuenciales")
                        for i, resp in enumerate(respuesta):
                            # Añadir una pausa entre mensajes para simular escritura
                            if i > 0:
                                # Pausa proporcional a la longitud del mensaje usando la configuración
                                pausa = config.TIEMPO_BASE_ENTRE_MENSAJES + min(len(resp) * config.FACTOR_TIEMPO_POR_CARACTER, config.TIEMPO_MAXIMO_ADICIONAL)
                                print(f"⏱️ Esperando {pausa:.2f} segundos antes del siguiente mensaje...")
                                time.sleep(pausa)

                            # Enviar este mensaje
                            enviar_respuesta(driver, resp)
                    else:
                        # Si es un solo mensaje, enviarlo normalmente
                        enviar_respuesta(driver, respuesta)

            # Esperar un tiempo configurado antes de verificar nuevos mensajes
            time.sleep(config.TIEMPO_VERIFICACION_MENSAJES)

    except KeyboardInterrupt:
        print("\n🛑 Bot detenido manualmente.")
        driver.quit()
    except Exception as e:
        print(f"❌ Error en el loop principal: {e}")
        driver.quit()

if __name__ == "__main__":
    ejecutar_bot()
