"""
Test para la funcionalidad de improvisación del bot.
Este script simula una conversación donde el bot improvisa historias.
"""

import sys
import os
import unittest
from unittest.mock import patch, MagicMock

# Añadir el directorio raíz al path para poder importar los módulos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Importar el módulo central del bot
from src.bot.core import (
    inicializar_bot,
    procesar_mensaje_y_generar_respuesta,
    detectar_intencion
)

# Importar el módulo de generación de historias
from src.utils.story_generator import (
    generar_historia_improvisada,
    generar_excusa_para_no_contar,
    decidir_contar_historia
)

class TestBotImprovisar(unittest.TestCase):
    """Clase para probar la funcionalidad de improvisación del bot."""

    def setUp(self):
        """Configuración inicial para las pruebas."""
        # Inicializar el bot con un archivo de historial temporal
        self.historial_archivo = "tests/historial_test_improvisar.json"
        inicializar_bot(self.historial_archivo)
        
        # ID de chat para la prueba
        self.chat_id = "test_improvisar"

    def tearDown(self):
        """Limpieza después de las pruebas."""
        # Eliminar el archivo de historial temporal si existe
        if os.path.exists(self.historial_archivo):
            os.remove(self.historial_archivo)

    def test_detectar_intencion_recordar(self):
        """Prueba la detección de intención de recordar algo."""
        # Mensajes que deberían detectarse como intención de recordar
        mensajes_recordar = [
            "me acabo de acordar de algo",
            "me he acordado de una cosa",
            "acabo de recordar algo gracioso",
            "me vino a la mente una anécdota",
            "recordé algo que te quería contar"
        ]
        
        for mensaje in mensajes_recordar:
            intencion = detectar_intencion(mensaje)
            self.assertEqual(intencion, "recordar", f"Falló con mensaje: {mensaje}")

    @patch('src.utils.story_generator.decidir_contar_historia')
    @patch('src.utils.story_generator.generar_historia_improvisada')
    def test_bot_cuenta_historia(self, mock_generar_historia, mock_decidir_contar):
        """Prueba que el bot cuenta una historia cuando se le pregunta."""
        # Configurar los mocks
        mock_decidir_contar.return_value = True
        historia_esperada = "El otro día me resbalé en plena calle y me acordé de cuando éramos pequeños"
        mock_generar_historia.return_value = historia_esperada
        
        # Simular una conversación donde el bot menciona que recuerda algo
        respuesta1 = procesar_mensaje_y_generar_respuesta("Hola, ¿qué tal?", self.chat_id, self.historial_archivo)
        self.assertIsNotNone(respuesta1)
        
        # Forzar una respuesta donde el bot dice que recuerda algo
        from src.bot.core import historial_conversaciones
        historial_conversaciones[self.chat_id].append({
            "role": "assistant",
            "content": "Todo bien, me acabo de acordar de algo super gracioso",
            "timestamp": "2025-05-14 22:00:00"
        })
        
        # El usuario pregunta sobre lo que recordó
        respuesta2 = procesar_mensaje_y_generar_respuesta("¿De qué te acordaste?", self.chat_id, self.historial_archivo)
        
        # Verificar que el bot cuenta la historia
        self.assertEqual(respuesta2, historia_esperada)
        mock_decidir_contar.assert_called_once()
        mock_generar_historia.assert_called_once()

    @patch('src.utils.story_generator.decidir_contar_historia')
    @patch('src.utils.story_generator.generar_excusa_para_no_contar')
    def test_bot_da_excusa(self, mock_generar_excusa, mock_decidir_contar):
        """Prueba que el bot da una excusa cuando decide no contar la historia."""
        # Configurar los mocks
        mock_decidir_contar.return_value = False
        excusa_esperada = "nah, es una tontería, mejor otro día"
        mock_generar_excusa.return_value = excusa_esperada
        
        # Simular una conversación donde el bot menciona que recuerda algo
        respuesta1 = procesar_mensaje_y_generar_respuesta("Hola, ¿cómo estás?", self.chat_id, self.historial_archivo)
        self.assertIsNotNone(respuesta1)
        
        # Forzar una respuesta donde el bot dice que recuerda algo
        from src.bot.core import historial_conversaciones
        historial_conversaciones[self.chat_id].append({
            "role": "assistant",
            "content": "Bien, acabo de recordar algo que pasó ayer",
            "timestamp": "2025-05-14 22:00:00"
        })
        
        # El usuario pregunta sobre lo que recordó
        respuesta2 = procesar_mensaje_y_generar_respuesta("Cuéntame, ¿qué recordaste?", self.chat_id, self.historial_archivo)
        
        # Verificar que el bot da una excusa
        self.assertEqual(respuesta2, excusa_esperada)
        mock_decidir_contar.assert_called_once()
        mock_generar_excusa.assert_called_once()

if __name__ == "__main__":
    unittest.main()
