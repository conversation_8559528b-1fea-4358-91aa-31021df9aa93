"""
Configuración centralizada para el bot de WhatsApp con Gemini.
Contiene todas las variables de configuración utilizadas en el proyecto.
"""

# Configuración de la API de Gemini
GEMINI_API_KEY = "AIzaSyDjtn7D8kDhvqt44mqJk0Awev0aCv7DYt0"
GEMINI_MODEL = "gemini-1.5-pro"

# Configuración del bot
MODO_SILENCIOSO_DEFAULT = False
MODO_DEBUG_DEFAULT = False
MAX_MENSAJES_HISTORIAL = 50

# Configuración de temperatura de IA
# La temperatura controla cuánto se adhiere el bot a su personalidad predefinida
# 0.0 = Estricta adherencia a la personalidad, 1.0 = Máxima creatividad y libertad
TEMPERATURA_DEFAULT = 0.5  # Valor predeterminado (50%)
TEMPERATURA_MIN = 0.0      # Mínima temperatura (0%)
TEMPERATURA_MAX = 1.0      # Máxima temperatura (100%)

# Configuración de la división de mensajes
MIN_PALABRAS_DIVIDIR = 20  # Mínimo de palabras para considerar dividir un mensaje
PROB_DIVIDIR_RESPUESTA = 0.3  # Probabilidad de dividir una respuesta larga (reducida)

# Configuración de errores tipográficos
PROB_ERROR_TIPOGRAFICO = 0.1  # Probabilidad de añadir un error tipográfico (reducida)

# Configuración de la división inteligente de mensajes
PALABRAS_INICIO_NUEVO_MENSAJE = ["pues", "pero", "y", "aunque", "sin embargo", "por cierto"]
MIN_PALABRAS_POR_MENSAJE = 8  # Mínimo de palabras que debe tener cada mensaje dividido (aumentado)
MAX_PALABRAS_POR_MENSAJE = 20  # Máximo de palabras que debe tener cada mensaje dividido (aumentado)

# Configuración de longitud de respuestas
MIN_PALABRAS_RESPUESTA_NORMAL = 8  # Mínimo de palabras para respuestas normales
MAX_PALABRAS_RESPUESTA_NORMAL = 20  # Máximo de palabras para respuestas normales
MIN_PALABRAS_RESPUESTA_ELABORADA = 25  # Mínimo de palabras para respuestas elaboradas
MAX_PALABRAS_RESPUESTA_ELABORADA = 45  # Máximo de palabras para respuestas elaboradas
