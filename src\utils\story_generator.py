"""
Módulo para generar historias improvisadas y anécdotas para el bot de WhatsApp.
Permite al bot inventar historias creíbles cuando menciona que se ha acordado de algo.
"""

import random

# Importar configuración centralizada
from src.config import config

# Categorías de historias que el bot puede generar
CATEGORIAS_HISTORIAS = [
    "anecdota_graciosa",
    "recuerdo_compartido",
    "situacion_incomoda",
    "coincidencia_curiosa",
    "idea_proyecto",
    "noticia_interesante",
    "sueño_extraño",
    "pensamiento_random"
]

# Plantillas de historias por categoría
PLANTILLAS_HISTORIAS = {
    "anecdota_graciosa": [
        "El otro día {evento_gracioso} y me acordé de cuando {evento_pasado}",
        "Estaba {actividad_cotidiana} y pasó algo súper gracioso: {evento_gracioso}",
        "Ayer {evento_gracioso} y no pude parar de reír pensando en ello",
        "Me pasó algo muy random: {evento_gracioso}. Todavía me río cuando lo pienso",
        "Estaba con {persona} y {evento_gracioso}, fue muy gracioso"
    ],
    "recuerdo_compartido": [
        "Me acordé de aquella vez que {evento_pasado_compartido}",
        "Te acuerdas cuando {evento_pasado_compartido}? Me vino a la mente de repente",
        "Estaba pensando en cuando {evento_pasado_compartido}, qué tiempos",
        "Me vino a la mente cuando {evento_pasado_compartido}, fue genial",
        "Recordé esa vez que {evento_pasado_compartido}, qué locura"
    ],
    "situacion_incomoda": [
        "Me pasó algo super incómodo: {situacion_incomoda}",
        "Ayer tuve un momento muy awkward: {situacion_incomoda}",
        "Estaba {actividad_cotidiana} y de repente {situacion_incomoda}",
        "Tuve una situación muy rara con {persona}: {situacion_incomoda}",
        "Pasé mucha vergüenza cuando {situacion_incomoda}"
    ],
    "coincidencia_curiosa": [
        "Qué casualidad más rara: {coincidencia}",
        "No te vas a creer la coincidencia que me pasó: {coincidencia}",
        "El mundo es un pañuelo: {coincidencia}",
        "Ayer me pasó algo que me dejó flipando: {coincidencia}",
        "Estaba {actividad_cotidiana} y {coincidencia}, qué locura"
    ],
    "idea_proyecto": [
        "Se me ocurrió una idea para un proyecto: {idea_proyecto}",
        "Estaba pensando en {idea_proyecto}, podría ser interesante",
        "Tuve una idea random: {idea_proyecto}. ¿Qué te parece?",
        "Estaba {actividad_cotidiana} y se me ocurrió {idea_proyecto}",
        "He estado dándole vueltas a {idea_proyecto}"
    ],
    "noticia_interesante": [
        "Leí algo interesante sobre {tema_noticia}: {detalle_noticia}",
        "¿Has visto lo de {tema_noticia}? {detalle_noticia}",
        "Me enteré de algo curioso: {detalle_noticia}",
        "Estaba leyendo sobre {tema_noticia} y {detalle_noticia}",
        "Vi una noticia sobre {tema_noticia} que me pareció muy interesante"
    ],
    "sueño_extraño": [
        "Tuve un sueño super raro: {descripcion_sueño}",
        "Soñé que {descripcion_sueño}, fue muy extraño",
        "Anoche tuve un sueño loquísimo donde {descripcion_sueño}",
        "No te vas a creer lo que soñé: {descripcion_sueño}",
        "Tuve un sueño tan random que {descripcion_sueño}"
    ],
    "pensamiento_random": [
        "Estaba pensando en {pensamiento_random}",
        "Me vino a la mente {pensamiento_random}",
        "Tuve un pensamiento random: {pensamiento_random}",
        "Estaba {actividad_cotidiana} y pensé en {pensamiento_random}",
        "De repente me acordé de {pensamiento_random}"
    ]
}

# Elementos para rellenar las plantillas
ELEMENTOS_HISTORIA = {
    "evento_gracioso": [
        "me resbalé en plena calle y la gente se me quedó mirando",
        "confundí a alguien con otra persona y le saludé efusivamente",
        "me quedé encerrado en el baño y tuve que pedir ayuda",
        "se me cayó el móvil en un charco y salté como loco a rescatarlo",
        "entré en la habitación equivocada y había gente dentro",
        "me puse la camiseta del revés y estuve así todo el día",
        "respondí a una pregunta que no era para mí en una reunión",
        "me quedé dormido en el transporte público y me pasé de parada",
        "intenté impresionar a alguien y acabé haciendo el ridículo",
        "me encontré con mi ex en la situación más random posible"
    ],
    "evento_pasado": [
        "fuimos a aquella fiesta donde acabamos todos cantando a las 5 de la mañana",
        "nos perdimos intentando encontrar aquel sitio y acabamos en otro lugar mejor",
        "intentamos cocinar algo elaborado y casi incendiamos la cocina",
        "pasamos toda la noche jugando y no nos dimos cuenta de la hora",
        "hicimos aquel viaje improvisado que salió sorprendentemente bien",
        "nos quedamos tirados en mitad de la nada y tuvimos que improvisar",
        "conocimos a esas personas tan peculiares en aquel bar",
        "intentamos arreglar algo y lo dejamos peor de lo que estaba",
        "nos metimos en aquel proyecto que parecía fácil y nos llevó semanas",
        "nos reímos tanto que nos echaron de aquel sitio"
    ],
    "actividad_cotidiana": [
        "haciendo la compra",
        "en el gimnasio",
        "trabajando en el ordenador",
        "esperando el transporte público",
        "cocinando algo rápido",
        "ordenando mi habitación",
        "dando un paseo",
        "viendo una serie",
        "escuchando música",
        "revisando el correo"
    ],
    "persona": [
        "un amigo del trabajo",
        "un colega de la universidad",
        "alguien que conocí hace poco",
        "un viejo amigo que hacía tiempo que no veía",
        "mi vecino",
        "un familiar",
        "un desconocido muy peculiar",
        "alguien que me presentaron recientemente",
        "un antiguo compañero de clase",
        "una persona que conocí en un evento"
    ],
    "evento_pasado_compartido": [
        "fuimos a aquel concierto y acabamos perdiendo el último transporte",
        "intentamos hacer aquella receta complicada y salió sorprendentemente bien",
        "nos quedamos hablando hasta las tantas sobre teorías conspiranoicas",
        "jugamos a ese juego que nos tuvo enganchados durante días",
        "vimos aquella película tan mala que nos reímos todo el rato",
        "fuimos a aquel sitio que parecía cutre pero resultó ser genial",
        "hicimos aquel proyecto juntos a última hora y salió mejor de lo esperado",
        "nos perdimos intentando encontrar aquel lugar y acabamos descubriendo otro mejor",
        "pasamos horas intentando resolver aquel problema técnico tan tonto",
        "nos encontramos por casualidad en el sitio más inesperado"
    ],
    "situacion_incomoda": [
        "me encontré con mi ex y su nueva pareja cuando iba en pijama a tirar la basura",
        "me confundieron con otra persona y me hablaron durante 5 minutos antes de darse cuenta",
        "entré en una reunión online con la cámara encendida sin darme cuenta",
        "saludé efusivamente a alguien que no era quien yo pensaba",
        "me quedé sin batería en medio de una conversación importante",
        "me pillaron cotilleando algo que no debería",
        "hice un comentario pensando que había colgado la llamada pero seguía conectado",
        "me quedé bloqueado en medio de una presentación y hubo un silencio eterno",
        "respondí a un mensaje que no era para mí y metí la pata",
        "me tropecé en público de la forma más aparatosa posible"
    ],
    "coincidencia": [
        "me encontré con alguien del colegio después de años en otro país",
        "estaba hablando de una persona y justo me llamó en ese momento",
        "compré el mismo objeto que un amigo el mismo día sin haberlo hablado",
        "vi a alguien idéntico a mí en la calle, como un doble",
        "descubrí que mi vecino y yo compartimos un amigo en común muy improbable",
        "estaba pensando en una canción antigua y justo empezó a sonar en la radio",
        "me recomendaron un libro que justo había empezado a leer esa mañana",
        "conocí a alguien nuevo y resulta que vivimos en la misma calle de pequeños",
        "vi a la misma persona random en tres sitios diferentes el mismo día",
        "encontré algo que había perdido hace años en el lugar más inesperado"
    ],
    "idea_proyecto": [
        "una app que te ayuda a encontrar sitios poco conocidos pero interesantes cerca de ti",
        "un sistema para organizar mejor el tiempo basado en ciclos de energía personal",
        "un podcast sobre historias curiosas que poca gente conoce",
        "un método para aprender idiomas combinando series y videojuegos",
        "una forma de reciclar objetos cotidianos en cosas útiles",
        "un juego de mesa que combina estrategia y contar historias",
        "una web que conecta a personas con habilidades complementarias para proyectos",
        "un sistema para automatizar tareas repetitivas del día a día",
        "una comunidad para intercambiar conocimientos especializados",
        "una herramienta para visualizar datos personales de forma útil y divertida"
    ],
    "tema_noticia": [
        "inteligencia artificial",
        "un descubrimiento científico",
        "una tecnología nueva",
        "un fenómeno cultural curioso",
        "un hallazgo arqueológico",
        "un avance médico",
        "un evento astronómico",
        "un comportamiento animal sorprendente",
        "un récord mundial inusual",
        "una tendencia social emergente"
    ],
    "detalle_noticia": [
        "aparentemente puede revolucionar cómo hacemos las cosas",
        "está cambiando la forma en que entendemos el mundo",
        "tiene implicaciones que no habíamos considerado antes",
        "resulta que es mucho más común de lo que pensábamos",
        "podría resolver problemas que creíamos imposibles",
        "está generando debates muy interesantes",
        "contradice lo que creíamos saber hasta ahora",
        "conecta ideas que parecían totalmente separadas",
        "está inspirando nuevas formas de pensar",
        "tiene un origen muy diferente al que imaginábamos"
    ],
    "descripcion_sueño": [
        "podía controlar el tiempo y lo usaba para cosas súper mundanas",
        "estaba en un lugar familiar pero todo funcionaba con reglas diferentes",
        "conocía a una versión alternativa de mí mismo que vivía una vida completamente distinta",
        "tenía una conversación profunda con alguien famoso sobre temas random",
        "descubría que tenía una habilidad oculta pero no sabía cómo usarla",
        "resolvía un problema complejo con una solución absurda que en el sueño tenía todo el sentido",
        "visitaba un lugar que nunca he visto pero me resultaba extrañamente familiar",
        "todos hablaban un idioma inventado que yo entendía perfectamente",
        "los objetos cotidianos cobraban vida y tenían personalidades muy definidas",
        "vivía en un mundo donde la física funcionaba de forma completamente diferente"
    ],
    "pensamiento_random": [
        "cómo sería la vida si hubiéramos tomado decisiones diferentes en momentos clave",
        "por qué algunas canciones nos conectan con recuerdos específicos tan intensamente",
        "cómo sería un día en la vida de personas con trabajos muy diferentes al nuestro",
        "si los hábitos que tenemos ahora definirán cómo seremos dentro de años",
        "qué pasaría si pudiéramos ver estadísticas de nuestra vida como en un videojuego",
        "cómo perciben el mundo personas con formas de pensar muy diferentes",
        "si realmente usamos todo el potencial de la tecnología que tenemos",
        "cómo serían las ciudades si se diseñaran desde cero hoy en día",
        "qué habilidades serán más valoradas en el futuro",
        "si hay experiencias universales que todas las personas viven de forma similar"
    ]
}

def generar_historia_improvisada(info_personal=None):
    """
    Genera una historia improvisada basada en la categoría seleccionada aleatoriamente.
    Si se proporciona información personal, la historia se adapta para ser más relevante.

    Args:
        info_personal (dict, opcional): Información personal del usuario para personalizar la historia.

    Returns:
        str: Historia improvisada generada.
    """
    # Seleccionar una categoría aleatoria
    categoria = random.choice(CATEGORIAS_HISTORIAS)

    # Seleccionar una plantilla aleatoria de esa categoría
    plantilla = random.choice(PLANTILLAS_HISTORIAS[categoria])

    # Reemplazar los marcadores de posición con elementos aleatorios
    historia = plantilla

    # Identificar todos los marcadores de posición en la plantilla
    marcadores = set()
    for marcador in plantilla.split("{")[1:]:
        marcador = marcador.split("}")[0]
        marcadores.add(marcador)

    # Reemplazar cada marcador con un elemento aleatorio
    for marcador in marcadores:
        if marcador in ELEMENTOS_HISTORIA:
            elemento = random.choice(ELEMENTOS_HISTORIA[marcador])
            historia = historia.replace("{" + marcador + "}", elemento)

    # Personalizar la historia si hay información personal disponible
    if info_personal and isinstance(info_personal, dict):
        # Personalizar con intereses si están disponibles
        if "personalidad" in info_personal and "intereses" in info_personal["personalidad"]:
            intereses = info_personal["personalidad"]["intereses"]
            if intereses and categoria == "idea_proyecto" and random.random() < 0.7:
                interes = random.choice(intereses)
                historia = historia.replace("una app", f"una app relacionada con {interes}")
                historia = historia.replace("un sistema", f"un sistema para {interes}")
                historia = historia.replace("un podcast", f"un podcast sobre {interes}")

        # Personalizar con expresiones favoritas si están disponibles
        if "estilo_comunicacion" in info_personal and "expresiones_favoritas" in info_personal["estilo_comunicacion"]:
            expresiones = info_personal["estilo_comunicacion"]["expresiones_favoritas"]
            if expresiones and random.random() < 0.3:
                expresion = random.choice(expresiones)
                if ", " in historia:
                    partes = historia.split(", ")
                    if len(partes) > 1:
                        partes[-1] = f"{expresion} {partes[-1]}"
                        historia = ", ".join(partes)

    return historia

def generar_excusa_para_no_contar():
    """
    Genera una excusa aleatoria para cuando el bot no quiere contar lo que "recordó".

    Returns:
        str: Excusa generada.
    """
    excusas = [
        "nah, es una tontería, mejor otro día",
        "mmm mejor no, que me da vergüenza jajsj",
        "era una chorrada, ni merece la pena",
        "mejor no, que luego te rayas conmigo jaja",
        "no tiene importancia, olvídalo",
        "mejor no te lo cuento que es un poco random",
        "nada importante, cosas mías",
        "era algo del trabajo, muy aburrido",
        "mejor hablamos de otra cosa, qué tal tú?",
        "prefiero no hablar de eso ahora mismo",
        "es que si te lo cuento no tiene gracia",
        "me da pereza explicarlo ahora jaja",
        "es largo de contar, otro día",
        "mejor lo dejamos para cuando nos veamos",
        "es que si te lo escribo pierde la gracia"
    ]
    return random.choice(excusas)

def decidir_contar_historia():
    """
    Decide aleatoriamente si contar la historia o dar una excusa.

    Returns:
        bool: True si se debe contar la historia, False si se debe dar una excusa.
    """
    # Usar la probabilidad configurada en el archivo de configuración
    return random.random() < config.PROB_CONTAR_HISTORIA
