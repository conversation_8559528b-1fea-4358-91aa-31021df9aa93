<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Bot - Simple</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background-color: #128C7E;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
        }
        .messages {
            height: 400px;
            overflow-y: auto;
            padding: 15px;
            background-color: #e5e5e5;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 5px;
            max-width: 80%;
            word-wrap: break-word;
        }
        .user {
            background-color: #DCF8C6;
            margin-left: auto;
        }
        .bot {
            background-color: white;
        }
        .input-area {
            display: flex;
            padding: 10px;
            border-top: 1px solid #e0e0e0;
        }
        input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
        }
        button {
            background-color: #128C7E;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        .status {
            padding: 10px;
            text-align: center;
            font-size: 12px;
            color: #666;
        }
        .controls {
            padding: 10px;
            border-top: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .temperature-control {
            display: flex;
            align-items: center;
            flex: 1;
        }
        .temperature-label {
            margin-right: 10px;
            font-size: 14px;
            color: #444;
            min-width: 150px;
        }
        .temperature-value {
            margin-left: 10px;
            font-size: 14px;
            color: #128C7E;
            font-weight: bold;
            min-width: 50px;
        }
        input[type="range"] {
            flex: 1;
            height: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">Test Bot - Interfaz Simple</div>
        <div class="messages" id="messages"></div>
        <div class="input-area">
            <input type="text" id="message-input" placeholder="Escribe un mensaje..." autofocus>
            <button id="send-button">Enviar</button>
        </div>
        <div class="controls">
            <div class="temperature-control">
                <div class="temperature-label">Temperatura de IA:</div>
                <input type="range" id="temperature-slider" min="0" max="100" value="50">
                <div class="temperature-value" id="temperature-value">50%</div>
            </div>
        </div>
        <div class="status" id="status">Listo para enviar mensajes</div>
    </div>

    <script>
        // Elementos del DOM
        const messagesContainer = document.getElementById('messages');
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        const statusElement = document.getElementById('status');
        const temperatureSlider = document.getElementById('temperature-slider');
        const temperatureValue = document.getElementById('temperature-value');

        // Chat ID
        const chatId = 'test_chat';

        // Función para añadir un mensaje a la interfaz
        function addMessage(role, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            messageDiv.textContent = content;
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Función para enviar un mensaje
        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            // Añadir mensaje del usuario a la interfaz
            addMessage('user', message);
            messageInput.value = '';
            messageInput.focus();

            // Actualizar estado
            statusElement.textContent = 'Enviando mensaje...';

            // Enviar mensaje al servidor
            fetch('http://127.0.0.1:5000/enviar_mensaje', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    mensaje: message,
                    chat_id: chatId
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Error: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Añadir la primera respuesta del bot a la interfaz
                if (data.respuesta) {
                    addMessage('bot', data.respuesta);
                }

                // Si hay mensajes adicionales, mostrarlos con retrasos
                if (data.tiene_mas_mensajes && data.mensajes_adicionales && data.mensajes_adicionales.length > 0) {
                    statusElement.textContent = 'Bot está escribiendo...';

                    // Mostrar cada mensaje adicional con un retraso
                    data.mensajes_adicionales.forEach((mensaje, index) => {
                        const delay = data.delays && data.delays[index] ? data.delays[index] : (index + 1) * 1000;

                        setTimeout(() => {
                            // Mostrar indicador de "escribiendo" antes de cada mensaje
                            statusElement.textContent = 'Bot está escribiendo...';

                            // Después de un breve retraso, mostrar el mensaje
                            setTimeout(() => {
                                addMessage('bot', mensaje);

                                // Si es el último mensaje, actualizar el estado
                                if (index === data.mensajes_adicionales.length - 1) {
                                    statusElement.textContent = 'Listo para enviar mensajes';
                                }
                            }, 300);
                        }, delay);
                    });
                } else {
                    statusElement.textContent = 'Listo para enviar mensajes';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                statusElement.textContent = `Error: ${error.message}`;
                addMessage('bot', `Error: ${error.message}`);
            });
        }

        // Evento para enviar mensaje con el botón
        sendButton.addEventListener('click', sendMessage);

        // Evento para enviar mensaje con Enter
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                sendMessage();
            }
        });

        // Función para actualizar la temperatura
        function updateTemperature(value) {
            temperatureValue.textContent = `${value}%`;

            // Enviar el valor de temperatura al servidor
            fetch('http://127.0.0.1:5000/set_temperature', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    temperature: parseInt(value)
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log(`Temperatura establecida a ${value}%`);
                    statusElement.textContent = `Temperatura establecida a ${value}%`;
                    setTimeout(() => {
                        statusElement.textContent = 'Listo para enviar mensajes';
                    }, 2000);
                } else {
                    console.error('Error al establecer temperatura:', data.error);
                    statusElement.textContent = `Error: ${data.error}`;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                statusElement.textContent = `Error: ${error.message}`;
            });
        }

        // Evento para el control deslizante de temperatura
        temperatureSlider.addEventListener('input', function() {
            temperatureValue.textContent = `${this.value}%`;
        });

        temperatureSlider.addEventListener('change', function() {
            updateTemperature(this.value);
        });

        // Cargar temperatura actual al iniciar
        fetch('http://127.0.0.1:5000/get_temperature')
            .then(response => response.json())
            .then(data => {
                if (data.temperature !== undefined) {
                    const tempValue = Math.round(data.temperature * 100);
                    temperatureSlider.value = tempValue;
                    temperatureValue.textContent = `${tempValue}%`;
                }
            })
            .catch(error => {
                console.error('Error al cargar temperatura:', error);
            });

        // Cargar historial al iniciar
        fetch(`http://127.0.0.1:5000/obtener_historial?chat_id=${chatId}`)
            .then(response => response.json())
            .then(data => {
                if (data.mensajes && data.mensajes.length > 0) {
                    data.mensajes.forEach(msg => {
                        addMessage(
                            msg.role === 'user' ? 'user' : 'bot',
                            msg.content
                        );
                    });
                    messagesContainer.scrollTop = messagesContainer.scrollHeight;
                }
            })
            .catch(error => {
                console.error('Error al cargar historial:', error);
                statusElement.textContent = 'Error al cargar historial';
            });
    </script>
</body>
</html>
