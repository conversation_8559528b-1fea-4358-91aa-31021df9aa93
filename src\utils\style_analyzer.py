"""
Módulo para analizar y aprender el estilo de escritura del usuario.
Este módulo implementa análisis lingüístico para capturar patrones de comunicación.
"""

import os
import re
import json
import string
import statistics
from datetime import datetime
from collections import Counter

# Importar configuración centralizada
from src.config import config

# Intentar importar spaCy para análisis lingüístico avanzado
try:
    import spacy
    # Intentar cargar el modelo español
    try:
        nlp = spacy.load("es_core_news_sm")
        SPACY_DISPONIBLE = True
        print("✅ Modelo de spaCy cargado correctamente")
    except:
        print("⚠️ Modelo de spaCy no encontrado. Instalando...")
        import subprocess
        subprocess.run(["python", "-m", "spacy", "download", "es_core_news_sm"])
        try:
            nlp = spacy.load("es_core_news_sm")
            SPACY_DISPONIBLE = True
            print("✅ Modelo de spaCy instalado y cargado correctamente")
        except:
            SPACY_DISPONIBLE = False
            print("❌ No se pudo cargar el modelo de spaCy. Usando análisis básico.")
except ImportError:
    SPACY_DISPONIBLE = False
    print("❌ spaCy no está instalado. Usando análisis básico.")

# Perfil de estilo global
perfil_estilo = {
    "metadata": {
        "ultima_actualizacion": "",
        "total_mensajes": 0,
        "sesiones": []
    },
    "estilo": {
        "capitalizacion": 0.5,  # Probabilidad de capitalizar
        "puntuacion": 0.5,      # Probabilidad de usar puntuación
        "abreviaciones": {},    # Abreviaciones comunes
        "emojis": {
            "frecuencia": 0.0,  # Frecuencia de uso de emojis
            "favoritos": [],    # Emojis favoritos
            "posicion": {       # Posición de los emojis
                "inicio": 0.0,
                "medio": 0.0,
                "final": 0.0
            }
        },
        "longitud_mensajes": {
            "promedio": 0,
            "desviacion": 0
        },
        "tiempo_respuesta": {
            "promedio": 0,
            "desviacion": 0
        }
    },
    "vocabulario": {
        "palabras_frecuentes": {},
        "frases_frecuentes": {}
    },
    "patrones": {
        "saludos": [],
        "despedidas": [],
        "afirmaciones": [],
        "negaciones": [],
        "preguntas": []
    }
}

def cargar_perfil_estilo():
    """Carga el perfil de estilo desde el archivo JSON."""
    global perfil_estilo
    try:
        if os.path.exists(config.PERFIL_ESTILO_ARCHIVO):
            with open(config.PERFIL_ESTILO_ARCHIVO, 'r', encoding='utf-8') as archivo:
                perfil_estilo = json.load(archivo)
                print(f"✅ Perfil de estilo cargado: {perfil_estilo['metadata']['total_mensajes']} mensajes analizados")
                return perfil_estilo
    except Exception as e:
        print(f"❌ Error al cargar perfil de estilo: {e}")
        # Inicializar con estructura vacía
        perfil_estilo = {
            "metadata": {
                "ultima_actualizacion": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "total_mensajes": 0,
                "sesiones": []
            },
            "estilo": {
                "capitalizacion": 0.5,
                "puntuacion": 0.5,
                "abreviaciones": {},
                "emojis": {
                    "frecuencia": 0.0,
                    "favoritos": [],
                    "posicion": {
                        "inicio": 0.0,
                        "medio": 0.0,
                        "final": 0.0
                    }
                },
                "longitud_mensajes": {
                    "promedio": 0,
                    "desviacion": 0
                },
                "tiempo_respuesta": {
                    "promedio": 0,
                    "desviacion": 0
                }
            },
            "vocabulario": {
                "palabras_frecuentes": {},
                "frases_frecuentes": {}
            },
            "patrones": {
                "saludos": [],
                "despedidas": [],
                "afirmaciones": [],
                "negaciones": [],
                "preguntas": []
            }
        }
    return perfil_estilo

def guardar_perfil_estilo(perfil=None):
    """Guarda el perfil de estilo en el archivo JSON."""
    global perfil_estilo
    try:
        # Si se proporciona un perfil, usarlo
        if perfil:
            perfil_estilo = perfil

        # Actualizar metadatos
        perfil_estilo["metadata"]["ultima_actualizacion"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        with open(config.PERFIL_ESTILO_ARCHIVO, 'w', encoding='utf-8') as archivo:
            json.dump(perfil_estilo, archivo, ensure_ascii=False, indent=2)
        print(f"✅ Perfil de estilo guardado: {perfil_estilo['metadata']['total_mensajes']} mensajes analizados")
        return True
    except Exception as e:
        print(f"❌ Error al guardar perfil de estilo: {e}")
        return False

def detectar_emojis(texto):
    """Detecta emojis en un texto."""
    if not texto:
        return []

    # Patrón para detectar emojis
    try:
        patron_emoji = re.compile(r'[\U0001F000-\U0001F9FF]')
        return patron_emoji.findall(texto)
    except Exception as e:
        print(f"❌ Error al detectar emojis: {e}")
        return []

def analizar_estilo(mensaje, chat_id, tiempo_respuesta=None):
    """Analiza el estilo de escritura de un mensaje y actualiza el perfil."""
    global perfil_estilo

    # Cargar perfil si no está inicializado
    if perfil_estilo["metadata"]["total_mensajes"] == 0 and os.path.exists(config.PERFIL_ESTILO_ARCHIVO):
        cargar_perfil_estilo()

    # Incrementar contador de mensajes
    perfil_estilo["metadata"]["total_mensajes"] += 1

    # Registrar la sesión si es nueva
    if chat_id not in perfil_estilo["metadata"]["sesiones"]:
        perfil_estilo["metadata"]["sesiones"].append(chat_id)

    # Análisis básico
    resultados_analisis = {}

    # 1. Análisis de capitalización
    primera_letra = mensaje[0] if mensaje and len(mensaje) > 0 else ""
    capitaliza = primera_letra.isupper() if primera_letra.isalpha() else False

    # Actualizar probabilidad de capitalización (media ponderada)
    n = perfil_estilo["metadata"]["total_mensajes"]
    peso_nuevo = min(config.PESO_NUEVOS_DATOS, 1.0 / max(1, n))
    peso_viejo = 1 - peso_nuevo

    perfil_estilo["estilo"]["capitalizacion"] = (
        perfil_estilo["estilo"]["capitalizacion"] * peso_viejo +
        (1 if capitaliza else 0) * peso_nuevo
    )

    resultados_analisis["capitalizacion"] = capitaliza

    # 2. Análisis de puntuación
    signos_puntuacion = [c for c in mensaje if c in ".,:;!?¿¡"]
    tiene_puntuacion = len(signos_puntuacion) > 0

    # Actualizar probabilidad de puntuación
    perfil_estilo["estilo"]["puntuacion"] = (
        perfil_estilo["estilo"]["puntuacion"] * peso_viejo +
        (1 if tiene_puntuacion else 0) * peso_nuevo
    )

    resultados_analisis["puntuacion"] = {
        "usa_puntuacion": tiene_puntuacion,
        "signos_usados": signos_puntuacion
    }

    # 3. Análisis de emojis
    emojis_en_mensaje = detectar_emojis(mensaje)
    tiene_emojis = len(emojis_en_mensaje) > 0

    # Actualizar frecuencia de emojis
    perfil_estilo["estilo"]["emojis"]["frecuencia"] = (
        perfil_estilo["estilo"]["emojis"]["frecuencia"] * peso_viejo +
        (1 if tiene_emojis else 0) * peso_nuevo
    )

    resultados_analisis["emojis"] = {
        "usa_emojis": tiene_emojis,
        "emojis_detectados": emojis_en_mensaje
    }

    # Actualizar emojis favoritos
    if tiene_emojis:
        # Crear lista de emojis favoritos si no existe
        if not perfil_estilo["estilo"]["emojis"]["favoritos"]:
            perfil_estilo["estilo"]["emojis"]["favoritos"] = []

        for e in emojis_en_mensaje:
            # Buscar si el emoji ya está en la lista
            emoji_existente = False
            for i, emoji_info in enumerate(perfil_estilo["estilo"]["emojis"]["favoritos"]):
                if emoji_info["emoji"] == e:
                    # Incrementar contador
                    perfil_estilo["estilo"]["emojis"]["favoritos"][i]["count"] += 1
                    emoji_existente = True
                    break

            if not emoji_existente:
                # Añadir nuevo emoji
                perfil_estilo["estilo"]["emojis"]["favoritos"].append({
                    "emoji": e,
                    "count": 1
                })

        # Ordenar por frecuencia
        perfil_estilo["estilo"]["emojis"]["favoritos"].sort(key=lambda x: x["count"], reverse=True)

        # Analizar posición de los emojis
        if emojis_en_mensaje:
            primer_emoji_pos = mensaje.find(emojis_en_mensaje[0])
            ultimo_emoji_pos = mensaje.rfind(emojis_en_mensaje[-1])
            longitud_mensaje = len(mensaje)

            # Determinar posición
            posicion = ""
            if primer_emoji_pos < longitud_mensaje * 0.2:
                perfil_estilo["estilo"]["emojis"]["posicion"]["inicio"] += 1
                posicion = "inicio"
            elif ultimo_emoji_pos > longitud_mensaje * 0.8:
                perfil_estilo["estilo"]["emojis"]["posicion"]["final"] += 1
                posicion = "final"
            else:
                perfil_estilo["estilo"]["emojis"]["posicion"]["medio"] += 1
                posicion = "medio"

            resultados_analisis["emojis"]["posicion"] = posicion

            # Normalizar posiciones
            total_posiciones = (
                perfil_estilo["estilo"]["emojis"]["posicion"]["inicio"] +
                perfil_estilo["estilo"]["emojis"]["posicion"]["medio"] +
                perfil_estilo["estilo"]["emojis"]["posicion"]["final"]
            )

            if total_posiciones > 0:
                perfil_estilo["estilo"]["emojis"]["posicion"]["inicio"] /= total_posiciones
                perfil_estilo["estilo"]["emojis"]["posicion"]["medio"] /= total_posiciones
                perfil_estilo["estilo"]["emojis"]["posicion"]["final"] /= total_posiciones

    # 4. Análisis de longitud de mensajes
    palabras = mensaje.split()
    longitud_palabras = len(palabras)

    # Actualizar longitud promedio
    longitud_anterior = perfil_estilo["estilo"]["longitud_mensajes"]["promedio"]
    perfil_estilo["estilo"]["longitud_mensajes"]["promedio"] = (
        longitud_anterior * peso_viejo + longitud_palabras * peso_nuevo
    )

    resultados_analisis["longitud"] = {
        "palabras": longitud_palabras,
        "caracteres": len(mensaje)
    }

    # Actualizar desviación estándar si hay suficientes mensajes
    if n >= 2:
        # Aproximación de la desviación estándar
        desviacion_actual = perfil_estilo["estilo"]["longitud_mensajes"]["desviacion"]
        diferencia = abs(longitud_palabras - perfil_estilo["estilo"]["longitud_mensajes"]["promedio"])
        nueva_desviacion = (desviacion_actual * peso_viejo + diferencia * peso_nuevo)
        perfil_estilo["estilo"]["longitud_mensajes"]["desviacion"] = nueva_desviacion

    # 5. Análisis de tiempo de respuesta (si se proporciona)
    if tiempo_respuesta is not None:
        tiempo_anterior = perfil_estilo["estilo"]["tiempo_respuesta"]["promedio"]
        perfil_estilo["estilo"]["tiempo_respuesta"]["promedio"] = (
            tiempo_anterior * peso_viejo + tiempo_respuesta * peso_nuevo
        )

        resultados_analisis["tiempo_respuesta"] = tiempo_respuesta

    # 6. Análisis de vocabulario
    # Extraer palabras (excluyendo stopwords comunes)
    stopwords = {"a", "al", "con", "de", "del", "el", "en", "es", "la", "las", "lo", "los", "me", "mi", "no", "por", "que", "se", "si", "su", "un", "una", "y"}
    palabras_relevantes = [p.lower() for p in palabras if p.lower() not in stopwords and len(p) > 2]

    # Actualizar palabras frecuentes
    for palabra in palabras_relevantes:
        if palabra in perfil_estilo["vocabulario"]["palabras_frecuentes"]:
            perfil_estilo["vocabulario"]["palabras_frecuentes"][palabra] += 1
        else:
            perfil_estilo["vocabulario"]["palabras_frecuentes"][palabra] = 1

    # Extraer frases (2-3 palabras consecutivas)
    for i in range(len(palabras) - 1):
        if i < len(palabras) - 2:
            frase_3 = " ".join(palabras[i:i+3]).lower()
            if frase_3 in perfil_estilo["vocabulario"]["frases_frecuentes"]:
                perfil_estilo["vocabulario"]["frases_frecuentes"][frase_3] += 1
            else:
                perfil_estilo["vocabulario"]["frases_frecuentes"][frase_3] = 1

        frase_2 = " ".join(palabras[i:i+2]).lower()
        if frase_2 in perfil_estilo["vocabulario"]["frases_frecuentes"]:
            perfil_estilo["vocabulario"]["frases_frecuentes"][frase_2] += 1
        else:
            perfil_estilo["vocabulario"]["frases_frecuentes"][frase_2] = 1

    resultados_analisis["vocabulario"] = {
        "palabras_relevantes": palabras_relevantes,
        "top_palabras": sorted(perfil_estilo["vocabulario"]["palabras_frecuentes"].items(), key=lambda x: x[1], reverse=True)[:5]
    }

    # 7. Análisis de patrones de mensaje
    if mensaje.endswith("?") or mensaje.endswith("¿"):
        tipo_mensaje = "pregunta"
    elif any(saludo in mensaje.lower() for saludo in ["hola", "hey", "buenas", "qué tal", "como estas"]):
        tipo_mensaje = "saludo"
    elif any(despedida in mensaje.lower() for despedida in ["adios", "chao", "hasta luego", "nos vemos", "bye"]):
        tipo_mensaje = "despedida"
    elif mensaje.lower().startswith("no ") or mensaje.lower() == "no":
        tipo_mensaje = "negacion"
    elif any(afirmacion in mensaje.lower() for afirmacion in ["si", "claro", "por supuesto", "exacto", "efectivamente"]):
        tipo_mensaje = "afirmacion"
    else:
        tipo_mensaje = "general"

    # Guardar el patrón
    if tipo_mensaje != "general":
        # Asegurarse de que la clave existe
        if tipo_mensaje not in perfil_estilo["patrones"]:
            perfil_estilo["patrones"][tipo_mensaje] = []

        # Añadir el mensaje si no está ya
        if mensaje not in perfil_estilo["patrones"][tipo_mensaje]:
            perfil_estilo["patrones"][tipo_mensaje].append(mensaje)

    resultados_analisis["tipo_mensaje"] = tipo_mensaje

    # 8. Análisis avanzado con spaCy (si está disponible)
    if SPACY_DISPONIBLE:
        try:
            doc = nlp(mensaje)

            # Análisis de estructura sintáctica
            estructura = []
            for token in doc:
                estructura.append({
                    "texto": token.text,
                    "pos": token.pos_,
                    "dep": token.dep_
                })

            resultados_analisis["analisis_spacy"] = {
                "estructura": estructura,
                "entidades": [(ent.text, ent.label_) for ent in doc.ents]
            }
        except Exception as e:
            print(f"❌ Error en análisis spaCy: {e}")

    # Guardar el perfil actualizado
    guardar_perfil_estilo()

    return resultados_analisis

def aplicar_estilo_a_respuesta(respuesta_original):
    """Aplica el estilo personal a una respuesta generada."""
    # Cargar perfil si no está inicializado
    if perfil_estilo["metadata"]["total_mensajes"] == 0:
        cargar_perfil_estilo()

    # Si no hay suficientes datos, devolver la respuesta original
    if perfil_estilo["metadata"]["total_mensajes"] < config.MIN_MENSAJES_ANALISIS:
        return respuesta_original

    # Aplicar transformaciones de estilo
    respuesta = respuesta_original

    # 1. Aplicar capitalización según el patrón del usuario
    if respuesta and len(respuesta) > 1:  # Asegurarse de que la respuesta no está vacía y tiene al menos 2 caracteres
        if perfil_estilo["estilo"]["capitalizacion"] < 0.3:
            # Usuario raramente capitaliza
            respuesta = respuesta[0].lower() + respuesta[1:]
        elif perfil_estilo["estilo"]["capitalizacion"] > 0.7:
            # Usuario casi siempre capitaliza
            respuesta = respuesta[0].upper() + respuesta[1:]

    # 2. Aplicar puntuación según el patrón del usuario
    if perfil_estilo["estilo"]["puntuacion"] < 0.3 and any(c in respuesta for c in ".,:;"):
        # Usuario usa poca puntuación - eliminar algunos signos
        respuesta = respuesta.replace(",", "").replace(";", "")
        if not respuesta.endswith("?") and not respuesta.endswith("!"):
            respuesta = respuesta.replace(".", "")

    # 3. Aplicar emojis según frecuencia y posición
    if perfil_estilo["estilo"]["emojis"]["frecuencia"] > 0.3 and perfil_estilo["estilo"]["emojis"]["favoritos"]:
        # Usuario usa emojis con frecuencia
        import random

        # Obtener emojis favoritos
        emojis_favoritos = [e["emoji"] for e in perfil_estilo["estilo"]["emojis"]["favoritos"][:5]]

        if emojis_favoritos:
            emoji_seleccionado = random.choice(emojis_favoritos)

            # Determinar posición según preferencia
            posiciones = perfil_estilo["estilo"]["emojis"]["posicion"]
            max_pos = max(posiciones.items(), key=lambda x: x[1])

            if max_pos[0] == "inicio":
                respuesta = emoji_seleccionado + " " + respuesta
            elif max_pos[0] == "final":
                respuesta = respuesta + " " + emoji_seleccionado
            else:
                # Insertar en medio
                palabras = respuesta.split()
                if len(palabras) > 2:
                    indice = len(palabras) // 2
                    palabras.insert(indice, emoji_seleccionado)
                    respuesta = " ".join(palabras)

    # 4. Ajustar longitud según preferencia del usuario
    longitud_objetivo = perfil_estilo["estilo"]["longitud_mensajes"]["promedio"]
    palabras = respuesta.split()

    if len(palabras) > longitud_objetivo * 1.5:
        # Respuesta demasiado larga, acortar
        respuesta = " ".join(palabras[:int(longitud_objetivo * 1.2)])
        if not respuesta.endswith((".", "!", "?")):
            respuesta += "..."

    return respuesta

# Cargar perfil al inicializar el módulo
cargar_perfil_estilo()
