"""
Módulo para análisis avanzado de vocabulario del bot de WhatsApp.
Implementa un enfoque híbrido que combina reglas específicas y Gemini.
"""

import re
import json
from datetime import datetime
import random
import os
import string

# Importar Gemini para análisis complejo
import google.generativeai as genai
# Importar configuración centralizada
from src.config import config

# Configurar la API de Gemini
genai.configure(api_key=config.GEMINI_API_KEY)

# Patrones para reconocimiento de situaciones comunicativas
PATRONES_SITUACIONES = {
    "saludo": ["hola", "hey", "buenas", "qué tal", "cómo estás", "saludos"],
    "despedida": ["adiós", "chao", "hasta luego", "nos vemos", "bye", "hasta mañana"],
    "pregunta": ["?", "qué", "cómo", "cuándo", "dónde", "por qué", "cuál"],
    "afirmación": ["sí", "claro", "efectivamente", "por supuesto", "exacto"],
    "negación": ["no", "nunca", "jamás", "para nada", "qué va"],
    "agradecimiento": ["gracias", "te lo agradezco", "muy amable"],
    "disculpa": ["perdón", "lo siento", "disculpa", "perdona"],
    "sorpresa": ["wow", "vaya", "increíble", "no me lo creo", "en serio"],
    "alegría": ["genial", "estupendo", "qué bien", "me alegro", "fantástico"],
    "tristeza": ["qué pena", "qué triste", "lo lamento", "qué mal"],
    "enfado": ["estoy enfadado", "me molesta", "qué rabia", "estoy harto"],
    "duda": ["quizás", "tal vez", "puede ser", "no estoy seguro", "a lo mejor"]
}

# Base de conocimiento para aprendizaje incremental
base_conocimiento = {}

def detectar_situacion(mensaje):
    """Detecta el tipo de situación comunicativa en un mensaje."""
    mensaje_lower = mensaje.lower()

    # Buscar coincidencias con patrones predefinidos
    for situacion, patrones in PATRONES_SITUACIONES.items():
        if any(patron in mensaje_lower for patron in patrones):
            return situacion

    # Si termina en interrogación, es una pregunta
    if mensaje.strip().endswith('?'):
        return "pregunta"

    # Por defecto, consideramos que es una afirmación/comentario general
    return "general"

def analizar_con_reglas(mensaje, palabra_nueva):
    """Realiza análisis sintáctico básico usando reglas lingüísticas."""

    # Obtener el contexto inmediato de la palabra
    palabras = mensaje.lower().split()
    try:
        indice = palabras.index(palabra_nueva.lower())
        palabras_anteriores = palabras[max(0, indice-3):indice]
        palabras_posteriores = palabras[indice+1:min(len(palabras), indice+4)]
    except ValueError:
        # Si la palabra no se encuentra exactamente, buscar por similitud
        indice = -1
        for i, p in enumerate(palabras):
            if p.startswith(palabra_nueva.lower()) or palabra_nueva.lower().startswith(p):
                indice = i
                break

        if indice >= 0:
            palabras_anteriores = palabras[max(0, indice-3):indice]
            palabras_posteriores = palabras[indice+1:min(len(palabras), indice+4)]
        else:
            palabras_anteriores = []
            palabras_posteriores = []

    # Análisis de categoría gramatical basado en sufijos y contexto
    categoria = "desconocida"

    # Sufijos comunes para diferentes categorías gramaticales
    sufijos_sustantivos = ['dad', 'ción', 'sión', 'miento', 'aje', 'ura', 'eza', 'or', 'ismo', 'ista']
    sufijos_adjetivos = ['oso', 'ble', 'ante', 'iente', 'al', 'ico', 'ivo', 'ado', 'ido']
    sufijos_verbos = ['ar', 'er', 'ir', 'ando', 'endo', 'ado', 'ido']
    sufijos_adverbios = ['mente']

    # Verificar sufijos
    if any(palabra_nueva.lower().endswith(sufijo) for sufijo in sufijos_sustantivos):
        categoria = "sustantivo"
    elif any(palabra_nueva.lower().endswith(sufijo) for sufijo in sufijos_adjetivos):
        categoria = "adjetivo"
    elif any(palabra_nueva.lower().endswith(sufijo) for sufijo in sufijos_verbos):
        categoria = "verbo"
    elif any(palabra_nueva.lower().endswith(sufijo) for sufijo in sufijos_adverbios):
        categoria = "adverbio"

    # Verificar contexto para refinar el análisis
    determinantes = ['el', 'la', 'los', 'las', 'un', 'una', 'unos', 'unas', 'mi', 'tu', 'su', 'este', 'ese', 'aquel']
    preposiciones = ['a', 'ante', 'bajo', 'con', 'contra', 'de', 'desde', 'en', 'entre', 'hacia', 'hasta', 'para', 'por', 'según', 'sin', 'sobre', 'tras']

    # Si hay determinantes antes, probablemente es un sustantivo
    if palabras_anteriores and palabras_anteriores[-1] in determinantes and categoria == "desconocida":
        categoria = "sustantivo"

    # Si hay preposiciones antes, podría ser sustantivo o verbo
    if palabras_anteriores and palabras_anteriores[-1] in preposiciones and categoria == "desconocida":
        categoria = "sustantivo"  # Asumimos sustantivo por defecto

    # Análisis de función sintáctica basado en contexto
    funcion = "desconocida"

    # Si está al inicio de la oración y es seguido por un verbo, podría ser sujeto
    if indice == 0 and palabras_posteriores and any(p.endswith(('a', 'e', 'o')) for p in palabras_posteriores[:2]):
        funcion = "sujeto"

    # Si está después de un verbo, podría ser objeto
    if palabras_anteriores and any(p.endswith(('a', 'e', 'o')) for p in palabras_anteriores[-2:]):
        funcion = "objeto"

    # Si está después de una preposición, es complemento preposicional
    if palabras_anteriores and palabras_anteriores[-1] in preposiciones:
        funcion = "complemento preposicional"

    # Intentar determinar el lemma (forma base)
    lemma = palabra_nueva.lower()

    # Para verbos, intentar obtener el infinitivo
    if categoria == "verbo":
        if palabra_nueva.endswith("ando") or palabra_nueva.endswith("endo"):
            # Gerundio
            if palabra_nueva.endswith("ando"):
                lemma = palabra_nueva[:-4] + "ar"
            else:
                lemma = palabra_nueva[:-4] + "er"
        elif palabra_nueva.endswith("ado") or palabra_nueva.endswith("ido"):
            # Participio
            if palabra_nueva.endswith("ado"):
                lemma = palabra_nueva[:-3] + "ar"
            else:
                lemma = palabra_nueva[:-3] + "er"

    return {
        "categoria_gramatical": categoria,
        "funcion_sintactica": funcion,
        "lemma": lemma
    }

def analizar_con_gemini(mensaje, palabra_nueva):
    """Utiliza Gemini para análisis más profundo en casos complejos."""
    try:
        prompt = f"""
        Analiza la palabra o expresión "{palabra_nueva}" en el siguiente mensaje:

        "{mensaje}"

        Proporciona un análisis lingüístico detallado con:
        1. Categoría gramatical (sustantivo, verbo, adjetivo, etc.)
        2. Función sintáctica en la oración
        3. Tipo de situación comunicativa (saludo, pregunta, afirmación, etc.)
        4. Registro (formal, informal, coloquial, etc.)
        5. Connotación (positiva, negativa, neutral)

        Responde en formato JSON con estos campos exactos, sin explicaciones adicionales.
        """

        modelo = genai.GenerativeModel(config.GEMINI_MODEL)
        respuesta = modelo.generate_content(prompt)

        # Intentar parsear la respuesta como JSON
        try:
            # Extraer solo el contenido JSON si está rodeado de texto
            texto_respuesta = respuesta.text.strip()
            # Buscar contenido entre llaves
            match = re.search(r'\{.*\}', texto_respuesta, re.DOTALL)
            if match:
                json_str = match.group(0)
                analisis = json.loads(json_str)
            else:
                analisis = json.loads(texto_respuesta)

            return analisis
        except json.JSONDecodeError:
            # Si no es JSON válido, extraer información clave mediante regex
            texto = respuesta.text
            categoria = re.search(r'categoría gramatical[:\s]+([\w\s]+)', texto, re.IGNORECASE)
            funcion = re.search(r'función sintáctica[:\s]+([\w\s]+)', texto, re.IGNORECASE)
            situacion = re.search(r'tipo de situación[:\s]+([\w\s]+)', texto, re.IGNORECASE)
            registro = re.search(r'registro[:\s]+([\w\s]+)', texto, re.IGNORECASE)
            connotacion = re.search(r'connotación[:\s]+([\w\s]+)', texto, re.IGNORECASE)

            return {
                "categoria_gramatical": categoria.group(1).strip() if categoria else "desconocida",
                "funcion_sintactica": funcion.group(1).strip() if funcion else "desconocida",
                "tipo_situacion": situacion.group(1).strip() if situacion else "desconocida",
                "registro": registro.group(1).strip() if registro else "desconocido",
                "connotacion": connotacion.group(1).strip() if connotacion else "neutral"
            }
    except Exception as e:
        print(f"❌ Error al analizar con Gemini: {e}")
        return None

def analizar_vocabulario(mensaje, palabra_nueva, frase_nueva=None):
    """
    Función principal que implementa el enfoque híbrido para análisis de vocabulario.
    Combina reglas específicas y Gemini según sea necesario.
    """
    # 1. Verificar si ya existe en la base de conocimiento
    if palabra_nueva in base_conocimiento:
        # Actualizar estadísticas
        base_conocimiento[palabra_nueva]["frecuencia"] += 1
        base_conocimiento[palabra_nueva]["contextos"].append(mensaje)

        # Si tenemos suficiente confianza, devolver el análisis existente
        if base_conocimiento[palabra_nueva]["confianza"] >= 0.7:
            return base_conocimiento[palabra_nueva]["analisis"]

    # 2. Detectar situación comunicativa mediante reglas
    situacion = detectar_situacion(mensaje)

    # 3. Realizar análisis sintáctico con reglas
    analisis_reglas = analizar_con_reglas(mensaje, palabra_nueva)

    # 4. Determinar si necesitamos análisis más profundo con Gemini
    necesita_gemini = False

    # Criterios para usar Gemini:
    # - Categoría gramatical desconocida
    # - Palabra compleja (longitud > 8)
    # - Palabra que aparece en contextos muy diversos
    if (analisis_reglas["categoria_gramatical"] == "desconocida" or
        len(palabra_nueva) > 8 or
        (palabra_nueva in base_conocimiento and len(base_conocimiento[palabra_nueva]["contextos"]) > 3)):
        necesita_gemini = True

    # 5. Si es necesario, complementar con análisis de Gemini
    analisis_gemini = None
    if necesita_gemini:
        analisis_gemini = analizar_con_gemini(mensaje, palabra_nueva)

    # 6. Combinar los análisis para obtener el resultado final
    analisis_final = {
        "categoria_gramatical": analisis_reglas["categoria_gramatical"],
        "funcion_sintactica": analisis_reglas["funcion_sintactica"],
        "lemma": analisis_reglas["lemma"],
        "tipo_situacion": situacion,
        "registro": "informal",  # Valor por defecto
        "connotacion": "neutral"  # Valor por defecto
    }

    # Si tenemos análisis de Gemini, complementar con información adicional
    if analisis_gemini:
        # Priorizar información de Gemini para campos donde el análisis por reglas no es confiable
        if analisis_reglas["categoria_gramatical"] == "desconocida":
            analisis_final["categoria_gramatical"] = analisis_gemini.get("categoria_gramatical", "desconocida")

        if analisis_reglas["funcion_sintactica"] == "desconocida":
            analisis_final["funcion_sintactica"] = analisis_gemini.get("funcion_sintactica", "desconocida")

        # Añadir información que solo proporciona Gemini
        analisis_final["registro"] = analisis_gemini.get("registro", "informal")
        analisis_final["connotacion"] = analisis_gemini.get("connotacion", "neutral")

    # 7. Almacenar en la base de conocimiento para aprendizaje incremental
    if palabra_nueva not in base_conocimiento:
        base_conocimiento[palabra_nueva] = {
            "frecuencia": 1,
            "primera_vez": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "ultima_vez": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "contextos": [mensaje],
            "analisis": analisis_final,
            "confianza": 0.5  # Confianza inicial media
        }
    else:
        # Actualizar el análisis si tenemos nueva información más confiable
        if necesita_gemini and analisis_gemini:
            base_conocimiento[palabra_nueva]["analisis"] = analisis_final
            base_conocimiento[palabra_nueva]["confianza"] = 0.8  # Mayor confianza con análisis de Gemini

        base_conocimiento[palabra_nueva]["ultima_vez"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    return analisis_final

def guardar_base_conocimiento(ruta_archivo):
    """Guarda la base de conocimiento en un archivo JSON."""
    try:
        with open(ruta_archivo, 'w', encoding='utf-8') as archivo:
            json.dump(base_conocimiento, archivo, ensure_ascii=False, indent=2)
        print(f"✅ Base de conocimiento guardada: {len(base_conocimiento)} palabras")
    except Exception as e:
        print(f"❌ Error al guardar base de conocimiento: {e}")

def cargar_base_conocimiento(ruta_archivo):
    """Carga la base de conocimiento desde un archivo JSON."""
    global base_conocimiento
    try:
        if os.path.exists(ruta_archivo):
            with open(ruta_archivo, 'r', encoding='utf-8') as archivo:
                base_conocimiento = json.load(archivo)
                print(f"✅ Base de conocimiento cargada: {len(base_conocimiento)} palabras")
    except Exception as e:
        print(f"❌ Error al cargar base de conocimiento: {e}")
        base_conocimiento = {}
