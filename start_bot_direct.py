#!/usr/bin/env python3
"""
Script directo para iniciar el bot de WhatsApp sin intermediarios.
"""

import sys
import os

# Agregar el directorio actual al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("🚀 Iniciando bot de WhatsApp directamente...")

try:
    # Importar y ejecutar directamente
    from src.interfaces.whatsapp import ejecutar_bot
    
    print("✅ Módulos importados correctamente")
    print("🔄 Ejecutando bot...")
    
    ejecutar_bot()
    
except Exception as e:
    print(f"❌ Error al iniciar el bot: {e}")
    import traceback
    traceback.print_exc()
    input("Presiona Enter para salir...")
