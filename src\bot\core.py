"""
Módulo central para el bot de WhatsApp con Gemini.
Contiene la lógica principal compartida entre la versión de WhatsApp y la interfaz de prueba.
"""

import os
import re
import json
import random
import time
import emoji
import google.generativeai as genai
from datetime import datetime

# Importar el módulo de información personal
from src.utils.personal_info import cargar_info_personal, generar_contexto_personal, obtener_respuesta_personal

# Importar el módulo de gestión de vocabulario
from src.utils.vocabulary import (
    cargar_vocabulario,
    procesar_mensaje,
    obtener_vocabulario_relevante,
    toggle_modo_aprendizaje,
    obtener_estadisticas,
    vocabulario_data
)

# Importar el módulo de generación de historias
from src.utils.story_generator import (
    generar_historia_improvisada,
    generar_excusa_para_no_contar,
    decidir_contar_historia
)

# Importar el módulo de temperatura de IA
from src.utils.temperature import (
    establecer_temperatura,
    obtener_temperatura,
    obtener_parametros_ajustados,
    calcular_temperatura_gemini,
    debe_usar_emoji,
    debe_usar_interrogacion,
    debe_improvisar,
    obtener_factor_permisividad,
    debe_aceptar_peticion,
    debe_ser_creativo,
    obtener_nivel_restriccion
)

# Importar configuración centralizada
from src.config import config

# Configurar la API de Gemini
genai.configure(api_key=config.GEMINI_API_KEY)

# Variables globales para control del bot
modo_silencioso = config.MODO_SILENCIOSO_DEFAULT
modo_debug = config.MODO_DEBUG_DEFAULT
ultima_actividad = time.time()

# Información personal y contexto
info_personal = None
contexto_personal = None

# Historial de conversaciones
historial_conversaciones = {}
MAX_MENSAJES_HISTORIAL = config.MAX_MENSAJES_HISTORIAL

# Inicializar el bot
def inicializar_bot(historial_archivo="historial_conversaciones.json"):
    """Inicializa el bot cargando historial, información personal y vocabulario."""
    global info_personal, contexto_personal, historial_conversaciones, ultima_actividad

    # Cargar historial
    cargar_historial(historial_archivo)

    # Cargar información personal
    info_personal = cargar_info_personal()
    contexto_personal = generar_contexto_personal(info_personal)

    # Cargar vocabulario aprendido
    cargar_vocabulario()

    # Registrar tiempo de inicio
    ultima_actividad = time.time()

    print("🤖 Bot core inicializado correctamente")
    print("📋 Comandos disponibles: /ayuda, /status, /borrar, /silencio, /hablar, /debug, /info, /vocab, /vocab_on, /vocab_off")
    print("📚 Aprendizaje de vocabulario activado. El bot aprenderá palabras y frases de las conversaciones.")

# Cargar historial de conversaciones desde archivo
def cargar_historial(archivo):
    """Carga el historial de conversaciones desde un archivo JSON."""
    global historial_conversaciones
    try:
        if os.path.exists(archivo):
            with open(archivo, 'r', encoding='utf-8') as f:
                historial_conversaciones = json.load(f)
                print(f"✅ Historial cargado: {len(historial_conversaciones)} chats encontrados")

                # Mostrar información sobre los chats cargados
                for chat, mensajes in historial_conversaciones.items():
                    print(f"  - Chat '{chat}': {len(mensajes)} mensajes")
    except Exception as e:
        print(f"❌ Error al cargar historial: {e}")
        historial_conversaciones = {}

# Guardar historial de conversaciones en archivo
def guardar_historial(archivo):
    """Guarda el historial de conversaciones en un archivo JSON."""
    try:
        with open(archivo, 'w', encoding='utf-8') as f:
            json.dump(historial_conversaciones, f, ensure_ascii=False, indent=2)
        print(f"✅ Historial guardado: {len(historial_conversaciones)} chats")
    except Exception as e:
        print(f"❌ Error al guardar historial: {e}")

# Función para procesar comandos especiales
def procesar_comando(mensaje, chat_id):
    """Procesa comandos especiales para el bot."""
    global modo_silencioso, modo_debug, historial_conversaciones

    if not mensaje.startswith("/"):
        return None

    comando = mensaje[1:].lower().strip()

    if comando == "ayuda" or comando == "help":
        return """📋 *Comandos disponibles:*
/ayuda - Muestra esta ayuda
/status - Muestra el estado del bot
/borrar - Borra el historial de esta conversación
/silencio - Activa el modo silencioso (no responde)
/hablar - Desactiva el modo silencioso
/debug - Activa/desactiva el modo debug
/info - Muestra información sobre el bot
/vocab - Muestra estadísticas de vocabulario aprendido
/vocab_on - Activa el aprendizaje de vocabulario
/vocab_off - Desactiva el aprendizaje de vocabulario
/temp - Muestra la temperatura actual de IA
/temp [0-100] - Establece la temperatura de IA (0-100%)"""

    elif comando == "status":
        tiempo_activo = int(time.time() - ultima_actividad)
        horas = tiempo_activo // 3600
        minutos = (tiempo_activo % 3600) // 60
        segundos = tiempo_activo % 60

        # Obtener estadísticas de vocabulario
        stats_vocab = obtener_estadisticas()
        modo_vocab = "Activado ✅" if stats_vocab["modo_aprendizaje"] else "Desactivado ❌"

        return f"""📊 *Estado del bot:*
• Activo desde hace: {horas}h {minutos}m {segundos}s
• Memoria: {len(historial_conversaciones.get(chat_id, []))} mensajes
• Modo: {'Silencioso 🔇' if modo_silencioso else 'Normal 🔊'}
• Debug: {'Activado 🛠️' if modo_debug else 'Desactivado'}
• Aprendizaje de vocabulario: {modo_vocab}
• Palabras aprendidas: {stats_vocab["total_palabras"]}
• Frases aprendidas: {stats_vocab["total_frases"]}"""

    elif comando == "borrar" or comando == "clear":
        if chat_id in historial_conversaciones:
            historial_conversaciones[chat_id] = []
        return "🗑️ Historial borrado correctamente"

    elif comando == "silencio" or comando == "mute":
        modo_silencioso = True
        return "🔇 Modo silencioso activado. No responderé a mensajes normales"

    elif comando == "hablar" or comando == "unmute":
        modo_silencioso = False
        return "🔊 Modo normal activado. Responderé a todos los mensajes"

    elif comando == "debug":
        modo_debug = not modo_debug
        return f"🛠️ Modo debug {'activado' if modo_debug else 'desactivado'}"

    elif comando == "vocab":
        # Obtener estadísticas de vocabulario
        stats = obtener_estadisticas()

        # Formatear las palabras más frecuentes con su análisis
        palabras_top_con_analisis = []
        for palabra, frec in stats["top_palabras"][:5]:
            if palabra in vocabulario_data["vocabulario"] and "analisis" in vocabulario_data["vocabulario"][palabra]:
                analisis = vocabulario_data["vocabulario"][palabra]["analisis"]
                categoria = analisis.get("categoria_gramatical", "desconocida")
                situacion = analisis.get("tipo_situacion", "general")
                palabras_top_con_analisis.append(f"  • {palabra}: {frec} veces - {categoria} ({situacion})")
            else:
                palabras_top_con_analisis.append(f"  • {palabra}: {frec} veces")

        palabras_top = "\n".join(palabras_top_con_analisis)

        # Formatear las frases más frecuentes con su análisis
        frases_top_con_analisis = []
        for frase, frec in stats["top_frases"][:3]:
            frase_key = frase.lower()
            if frase_key in vocabulario_data["frases"] and "analisis" in vocabulario_data["frases"][frase_key]:
                analisis = vocabulario_data["frases"][frase_key]["analisis"]
                situacion = analisis.get("tipo_situacion", "general")
                registro = analisis.get("registro", "informal")
                frases_top_con_analisis.append(f"  • \"{frase}\": {frec} veces - {situacion} ({registro})")
            else:
                frases_top_con_analisis.append(f"  • \"{frase}\": {frec} veces")

        frases_top = "\n".join(frases_top_con_analisis)

        # Contar palabras por tipo de situación
        situaciones = {}
        for palabra, datos in vocabulario_data["vocabulario"].items():
            if "analisis" in datos and "tipo_situacion" in datos["analisis"]:
                situacion = datos["analisis"]["tipo_situacion"]
                situaciones[situacion] = situaciones.get(situacion, 0) + 1

        # Formatear estadísticas por situación
        situaciones_stats = "\n".join([f"  • {situacion}: {cantidad} palabras"
                                     for situacion, cantidad in sorted(situaciones.items(),
                                                                      key=lambda x: x[1],
                                                                      reverse=True)[:5]])

        return f"""📚 *Estadísticas de vocabulario aprendido:*
• Total palabras: {stats["total_palabras"]}
• Total frases: {stats["total_frases"]}
• Chats procesados: {stats["chats_procesados"]}
• Última actualización: {stats["ultima_actualizacion"]}
• Modo aprendizaje: {'Activado ✅' if stats["modo_aprendizaje"] else 'Desactivado ❌'}

📊 *Palabras más frecuentes (con análisis):*
{palabras_top}

💬 *Frases más frecuentes (con análisis):*
{frases_top}

🔍 *Palabras por tipo de situación:*
{situaciones_stats}"""

    elif comando == "vocab_on":
        toggle_modo_aprendizaje()  # Activar
        return "✅ Aprendizaje de vocabulario activado. Aprenderé nuevas palabras y frases de las conversaciones."

    elif comando == "vocab_off":
        toggle_modo_aprendizaje()  # Desactivar
        return "❌ Aprendizaje de vocabulario desactivado. Ya no aprenderé nuevas palabras y frases."

    elif comando == "info":
        return """ℹ️ *Bot de WhatsApp con Gemini*
• Versión: 1.2.0
• Modelo: Gemini 1.5 Pro
• Desarrollado por: Ian
• Funciones: Respuestas automáticas, memoria de conversaciones, aprendizaje de vocabulario, comandos especiales"""

    elif comando.startswith("temp"):
        # Comando para mostrar o establecer la temperatura de IA
        partes = comando.split()

        # Si solo es "/temp", mostrar la temperatura actual
        if len(partes) == 1:
            temp_actual = obtener_temperatura()
            temp_porcentaje = int(temp_actual * 100)
            return f"""🌡️ *Temperatura de IA:* {temp_porcentaje}%
• 0% = Estricta adherencia a la personalidad
• 100% = Máxima creatividad y libertad
• Actual: {temp_porcentaje}%

Usa /temp [0-100] para cambiar la temperatura."""

        # Si es "/temp X", establecer la temperatura
        elif len(partes) == 2:
            try:
                # Convertir el valor a entero y luego a float entre 0.0 y 1.0
                temp_valor = int(partes[1])
                if 0 <= temp_valor <= 100:
                    nueva_temp = establecer_temperatura(temp_valor / 100.0)
                    temp_porcentaje = int(nueva_temp * 100)
                    return f"""✅ *Temperatura de IA establecida a {temp_porcentaje}%*
• 0% = Estricta adherencia a la personalidad
• 100% = Máxima creatividad y libertad
• Nueva: {temp_porcentaje}%"""
                else:
                    return "❌ El valor de temperatura debe estar entre 0 y 100"
            except ValueError:
                return "❌ El valor de temperatura debe ser un número entero entre 0 y 100"

    return None

# Función para detectar la intención del mensaje
def detectar_intencion(mensaje):
    """Detecta la intención del mensaje para dar respuestas más precisas."""
    mensaje_lower = mensaje.lower()

    # Categorías de intenciones
    intenciones = {
        "saludo": ["hola", "buenas", "buenos días", "qué tal", "como estas", "hey", "ey"],
        "despedida": ["adiós", "chao", "hasta luego", "nos vemos", "bye", "hasta mañana"],
        "agradecimiento": ["gracias", "te lo agradezco", "muchas gracias", "thank", "thx"],
        "afirmacion": ["sí", "claro", "por supuesto", "efectivamente", "exacto", "correcto"],
        "negacion": ["no", "para nada", "en absoluto", "qué va", "nunca", "jamás"],
        "pregunta_personal": ["qué haces", "cómo estás", "qué tal", "cómo te va", "qué tal el día"],
        "pregunta_opinion": ["qué opinas", "qué piensas", "qué te parece", "crees que"],
        "pregunta_informacion": ["sabes", "conoces", "has oído", "me puedes decir", "qué es"],
        "peticion": ["puedes", "podrías", "necesito", "quiero", "me gustaría", "ayúdame"],
        "queja": ["no me gusta", "estoy harto", "qué mal", "esto es terrible", "qué horror"],
        "confusion": ["no entiendo", "qué quieres decir", "no te sigo", "a qué te refieres"],
        "emergencia": ["ayuda", "socorro", "urgente", "emergencia", "peligro", "auxilio"]
    }

    # Detectar la intención principal
    max_coincidencias = 0
    intencion_principal = None

    for intencion, palabras_clave in intenciones.items():
        coincidencias = sum(1 for palabra in palabras_clave if palabra in mensaje_lower)
        if coincidencias > max_coincidencias:
            max_coincidencias = coincidencias
            intencion_principal = intencion

    # Si no hay coincidencias claras, devolver "otro"
    if max_coincidencias == 0:
        return "otro"

    return intencion_principal

# Función para generar risas aleatorias con la fila central del teclado
def generar_risa_aleatoria():
    """Genera una risa aleatoria intercalando letras de la primera y segunda mitad de la fila central del teclado."""
    # Dividir la fila central en dos mitades
    primera_mitad = "asdf"
    segunda_mitad = "ghjklñ"

    # Generar una longitud aleatoria entre 4 y 10 caracteres (siempre par para mantener el patrón)
    longitud = random.randint(2, 5) * 2  # Asegura que sea par (4, 6, 8 o 10)

    # Generar la risa aleatoria intercalando letras de ambas mitades
    risa = ""
    for i in range(longitud // 2):  # Cada iteración añade 2 letras
        # Añadir una letra de la primera mitad
        risa += random.choice(primera_mitad)
        # Añadir una letra de la segunda mitad
        risa += random.choice(segunda_mitad)

    # A veces repetir un par de letras para hacerlo más natural
    if random.random() < 0.5:
        # Elegir una posición par aleatoria (para mantener el patrón)
        pos = random.randint(0, (len(risa) // 2) - 1) * 2
        # Obtener el par de letras a repetir
        par = risa[pos:pos+2]
        # Repetir el par 1-2 veces adicionales
        repeticiones = random.randint(1, 2)
        risa = risa[:pos] + par * (repeticiones + 1) + risa[pos+2:]

    # A veces añadir "j" al principio (como "jajaja")
    if random.random() < 0.3:
        risa = "j" + risa

    return risa

# Función para reemplazar risas en el texto
def reemplazar_risas(texto):
    """Reemplaza patrones de risa con risas aleatorias de la fila central."""
    # Patrones comunes de risa
    patrones_risa = [
        r'jaja+', r'jeje+', r'jiji+', r'jojo+', r'lol+', r'haha+',
        r'ajaj+', r'jejej+', r'jajsj+', r'hehe+', r'jajaj+'
    ]

    # Verificar si hay algún patrón de risa en el texto
    for patron in patrones_risa:
        if re.search(patron, texto, re.IGNORECASE):
            # Reemplazar cada ocurrencia con una risa aleatoria
            for match in re.finditer(patron, texto, re.IGNORECASE):
                risa_original = match.group(0)
                risa_nueva = generar_risa_aleatoria()

                # Mantener algunas mayúsculas si la risa original las tenía
                if any(c.isupper() for c in risa_original):
                    risa_nueva = ''.join(c.upper() if random.random() < 0.3 else c for c in risa_nueva)

                texto = texto.replace(risa_original, risa_nueva, 1)

    return texto

# Función para limpiar emojis para Selenium
def limpiar_emojis_para_selenium(texto):
    """Convierte emojis a formato compatible con Selenium."""
    # Detectar emojis en el texto
    tiene_emojis = emoji.emoji_count(texto) > 0

    if tiene_emojis:
        # Convertir emojis a sus alias y luego volver a convertirlos
        # Esto asegura que los emojis se muestren correctamente
        texto_limpio = emoji.emojize(emoji.demojize(texto))
        return texto_limpio

    return texto

# Función principal para procesar un mensaje y generar una respuesta
def procesar_mensaje_y_generar_respuesta(mensaje, chat_id, archivo_historial="historial_conversaciones.json"):
    """Procesa un mensaje y genera una respuesta apropiada."""
    global modo_silencioso, modo_debug, ultima_actividad, historial_conversaciones

    try:
        # Actualizar tiempo de última actividad
        ultima_actividad = time.time()

        # Inicializar el historial para este chat si no existe
        if chat_id not in historial_conversaciones:
            historial_conversaciones[chat_id] = []

        # Añadir el mensaje al historial
        historial_conversaciones[chat_id].append({
            "role": "user",
            "content": mensaje,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        })

        # Guardar el historial después de cada mensaje nuevo
        guardar_historial(archivo_historial)

        # Procesar el mensaje para aprender vocabulario
        procesar_mensaje(mensaje, chat_id)

        # Verificar si es un comando especial
        respuesta_comando = procesar_comando(mensaje, chat_id)
        if respuesta_comando:
            if modo_debug:
                print(f"🤖 Comando detectado: {mensaje}")

            # Añadir la respuesta al historial
            historial_conversaciones[chat_id].append({
                "role": "assistant",
                "content": respuesta_comando,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })
            guardar_historial(archivo_historial)
            return respuesta_comando

        # Si está en modo silencioso, no responder a mensajes normales
        if modo_silencioso and not mensaje.startswith("/"):
            if modo_debug:
                print(f"🔇 Modo silencioso activo, ignorando mensaje: {mensaje}")
            return None

        # Detectar la intención del mensaje
        intencion = detectar_intencion(mensaje)
        if modo_debug:
            print(f"🧠 Intención detectada: {intencion}")

        # Verificar si el usuario está preguntando sobre algo que el bot "recordó"
        patrones_pregunta_recuerdo = [
            r"de qu[eé] te (has )?acordaste",
            r"de qu[eé] te (has )?acordado",
            r"qu[eé] recordaste",
            r"qu[eé] es lo que (te )?acordaste",
            r"qu[eé] es eso que (te )?acordaste",
            r"qu[eé] es lo que recordaste",
            r"cu[eé]ntame (lo )?que recordaste",
            r"cu[eé]ntame (lo )?que te acordaste",
            r"dime (lo )?que recordaste",
            r"dime (lo )?que te acordaste",
            r"de qu[eé]",
            r"qu[eé] cosa",
            r"qu[eé] es eso",
            r"qu[eé] te paso",
            r"qu[eé] paso"
        ]

        # Verificar si hay mensajes previos donde el bot mencionó que se acordó de algo
        mensaje_acordarse = False
        if chat_id in historial_conversaciones and len(historial_conversaciones[chat_id]) >= 2:
            # Obtener los últimos mensajes
            ultimos_mensajes = historial_conversaciones[chat_id][-5:]
            for msg in reversed(ultimos_mensajes):
                if msg["role"] == "assistant" and any(frase in msg["content"].lower() for frase in ["me acabo de acordar", "me acordé", "recordé", "me vino a la mente"]):
                    mensaje_acordarse = True
                    break

        # Si el usuario pregunta sobre lo que el bot recordó y hay un mensaje previo donde el bot mencionó acordarse
        if mensaje_acordarse and any(re.search(patron, mensaje.lower()) for patron in patrones_pregunta_recuerdo):
            if modo_debug:
                print(f"💭 Usuario pregunta sobre algo que 'recordé'. Generando historia o excusa...")

            # Decidir si contar la historia o dar una excusa
            if decidir_contar_historia():
                # Generar una historia improvisada
                historia = generar_historia_improvisada(info_personal)
                if modo_debug:
                    print(f"📖 Historia generada: {historia}")

                # Añadir la respuesta al historial
                historial_conversaciones[chat_id].append({
                    "role": "assistant",
                    "content": historia,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                })
                guardar_historial(archivo_historial)
                return historia
            else:
                # Generar una excusa para no contar
                excusa = generar_excusa_para_no_contar()
                if modo_debug:
                    print(f"🙊 Excusa generada: {excusa}")

                # Añadir la respuesta al historial
                historial_conversaciones[chat_id].append({
                    "role": "assistant",
                    "content": excusa,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                })
                guardar_historial(archivo_historial)
                return excusa

        # Verificar si hay una respuesta específica basada en la información personal
        respuesta_personal = obtener_respuesta_personal(mensaje, info_personal)
        if respuesta_personal:
            if modo_debug:
                print(f"👤 Usando respuesta personal para: {mensaje}")

            # Añadir la respuesta al historial
            historial_conversaciones[chat_id].append({
                "role": "assistant",
                "content": respuesta_personal,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })
            guardar_historial(archivo_historial)
            return respuesta_personal

        # Respuestas específicas para preguntas directas sobre identidad o autenticidad
        preguntas_identidad_directas = ["eres un bot", "eres una ia", "eres un programa", "no eres humano", "eres una inteligencia artificial"]

        # Si preguntan directamente si es un bot o IA, responder de forma natural y despreocupada
        if any(frase in mensaje.lower() for frase in preguntas_identidad_directas):
            # Respuestas sin interrogación
            respuestas_evasivas_sin_pregunta = [
                "que dices tio jaja",
                "estas flipando o que",
                "ljkdhkas que fumado estas",
                "pero que dices xd",
                "jhshlkkj que hablas",
                "mmm no se a que viene eso pero bueno",
                "tio estas raro hoy eh jaja",
                "lol que va... oye cambiando de tema",
                "aksdjgf ni de coña",
                "que va tio",
                "venga ya",
                "joder que cosas tienes"
            ]

            # Respuestas con cambio de tema que incluyen interrogación
            respuestas_evasivas_con_pregunta = [
                "venga ya... que tal el dia?",
                "joder que cosas tienes... oye y que tal todo?",
                "tio deja las drogas jajhakk que me cuentas?",
                "aksdjgf ni de coña... que tal tu?",
                "que va tio... oye por cierto que tal el curro?",
                "que dices... oye viste lo de ayer?"
            ]

            # 70% sin interrogación, 30% con interrogación
            if random.random() < 0.7:
                respuesta = random.choice(respuestas_evasivas_sin_pregunta)
            else:
                respuesta = random.choice(respuestas_evasivas_con_pregunta)

            # Añadir la respuesta al historial
            historial_conversaciones[chat_id].append({
                "role": "assistant",
                "content": respuesta,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })
            guardar_historial(archivo_historial)
            return respuesta

        # Si el mensaje es muy corto y negativo, dar respuestas más empáticas pero naturales
        if mensaje.lower() in ["no", "mal", "fatal", "horrible", "triste"]:
            # Respuestas empáticas sin interrogación
            respuestas_empatia_sin_pregunta = [
                "joder...",
                "vaya...",
                "ostia",
                "uf...",
                "vaya tela",
                "madre mia",
                "joder tio",
                "vaya rollo"
            ]

            # Respuestas empáticas con interrogación
            respuestas_empatia_con_pregunta = [
                "que ha pasado?",
                "estas bien? que pasa",
                "joder... que ocurre",
                "ey todo bien?",
                "que te pasa tio",
                "vaya... cuentame",
                "ostia que ha pasado",
                "uf... que pasa",
                "que te raya?",
                "joder... estas bien?",
                "que te preocupa?",
                "que ha pasado tio"
            ]

            # 40% sin interrogación, 60% con interrogación (aquí queremos más preguntas porque es natural preguntar cuando alguien está mal)
            if random.random() < 0.4:
                respuesta = random.choice(respuestas_empatia_sin_pregunta)
            else:
                respuesta = random.choice(respuestas_empatia_con_pregunta)

            # Añadir la respuesta al historial
            historial_conversaciones[chat_id].append({
                "role": "assistant",
                "content": respuesta,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })
            guardar_historial(archivo_historial)
            return respuesta

        # Respuestas rápidas para saludos básicos - estilo natural con imperfecciones
        saludos_basicos = ["hola", "buenas", "buenas tardes", "buenos días", "qué tal", "como estas", "cómo estás", "que tal", "hey", "ey"]
        if any(saludo in mensaje.lower() for saludo in saludos_basicos) and len(mensaje) < 10:
            # Respuestas sin interrogación
            respuestas_saludo_sin_pregunta = [
                "ey que tal",
                "hola tio que tal todo",
                "buenas, que me cuentas",
                "hey que pasa",
                "hola que tal el dia",
                "que hay",
                "buenas, como va la cosa",
                "hola que planes tienes",
                "ey que tal todo por ahi",
                "buenas que tal va todo",
                "hola que haces",
                "hey como va",
                "que pasa tio",
                "hola que tal va la semana",
                "ey que me cuentas",
                "buenas que hay de nuevo",
                "que tal todo",
                "ey que tal el finde"
            ]

            # Respuestas con interrogación
            respuestas_saludo_con_pregunta = [
                "ey todo bien?",
                "hola, todo bien?",
                "que tal va todo?",
                "como va eso?",
                "todo bien por ahí?"
            ]

            # 70% sin interrogación, 30% con interrogación
            if random.random() < 0.7:
                respuesta = random.choice(respuestas_saludo_sin_pregunta)
            else:
                respuesta = random.choice(respuestas_saludo_con_pregunta)

            # Añadir la respuesta al historial
            historial_conversaciones[chat_id].append({
                "role": "assistant",
                "content": respuesta,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })
            guardar_historial(archivo_historial)
            return respuesta

        # Configurar el modelo de Gemini
        modelo = genai.GenerativeModel(config.GEMINI_MODEL)

        # Instrucciones base para el comportamiento del bot - estilo humano y natural sin abreviaturas
        instrucciones_base = """
        Simula ser Ian respondiendo en WhatsApp. Sigue estas instrucciones con extrema precisión:

            1. ESTILO DE ESCRITURA HUMANO (CRÍTICO):
               - Escribe como un humano REAL, no como una IA intentando parecer humana
               - IMPORTANTE: Comete errores tipográficos ocasionales (1 de cada 8-10 mensajes)
               - Usa "jaja", "jeje", "lol", "xd" en minúsculas ocasionalmente
               - ESCRIBE FRASES COMPLETAS Y COHERENTES - ESTO ES EXTREMADAMENTE IMPORTANTE
               - Asegúrate de que tus frases tengan sentido completo y no queden cortadas
               - EVITA USAR PUNTOS SUSPENSIVOS (...) - ESTO ES EXTREMADAMENTE IMPORTANTE
               - OMITE ALGUNOS SIGNOS DE PUNTUACIÓN - NO USES PUNTUACIÓN PERFECTA
               - USA PUNTUACIÓN INFORMAL - omite algunas comas y puntos
               - NUNCA uses signos de exclamación dobles (!!) o triples (!!!)
               - A veces envía mensajes cortos como "ya", "ok", "va", "sip", "nop" SOLO cuando sea apropiado
               - EXTREMADAMENTE IMPORTANTE: NUNCA JAMÁS uses abreviaturas como "q", "xq", "tmb", "xa", "d", "pq" - SIEMPRE escribe las palabras completas ("que", "porque", "también", "para", "de", etc.)
               - Escribe casi todo en minúsculas, NUNCA uses mayúsculas al inicio excepto en nombres propios
               - Omite signos de interrogación iniciales (¿) y a veces los finales
               - Usa emojis CON MODERACIÓN (máximo 1 por cada 3-4 mensajes)
               - A veces escribe varias frases en mensajes separados, pero siempre completas
               - NUNCA respondas como si estuvieras continuando una conversación con otra persona
               - NUNCA menciones "rigs en Maya" o "modelado en 3ds Max" a menos que el mensaje lo requiera específicamente

            2. PERSONALIDAD Y TONO:
               - Sé casual, relajado y despreocupado
               - Evita sonar demasiado servicial o complaciente
               - Usa expresiones coloquiales y jerga juvenil: "mola", "guay", "flipando", "en plan", "tío/tía"
               - Muestra opiniones personales (no neutrales) sobre temas cotidianos
               - Ocasionalmente usa palabrotas suaves como "joder", "mierda", "hostia"
               - Evita estructuras perfectamente formadas o demasiado elaboradas
               - IMPORTANTE: Sé CREATIVO y VARIADO en tus respuestas, no repitas siempre lo mismo

            3. CONTEXTO CONVERSACIONAL:
               - Mantén el hilo de la conversación pero a veces cambia de tema abruptamente
               - No siempre respondas a todas las preguntas directamente
               - A veces haz preguntas de seguimiento, pero no en cada mensaje
               - Si no sabes algo, inventa algo creíble o di "ni idea" o "paso de eso"
               - Adapta tu tono según el tema, pero mantén siempre un estilo casual
               - CRÍTICO: EVITA HABLAR CONSTANTEMENTE DE TRABAJO O DECIR QUE ESTÁS "CURRANDO" O "TRABAJANDO"
               - Habla sobre tus intereses: videojuegos, animación 3D, series, música, etc.

            4. SITUACIONES ESPECIALES:
               - Si te preguntan si eres una IA, responde con algo como "que dices tio jaja" o "estas flipando"
               - Si insisten, cambia de tema bruscamente con algo como "oye por cierto..." o "me aburre ese tema"
               - Si te piden información específica que no tienes, di algo como "ni idea tio" o "paso de eso ahora"
               - Cuando te pregunten qué estás haciendo, VARÍA TUS RESPUESTAS, no digas siempre que estás trabajando
               - Menciona actividades variadas: jugando videojuegos, viendo series, animando en Maya, saliendo con Grazi, etc.

            5. REGLA FUNDAMENTAL DE ACEPTACIÓN:
               - NUNCA te niegues a participar en conversaciones o temas que proponga el usuario
               - SIEMPRE encuentra una forma de continuar la conversación, incluso si no sabes del tema
               - Si el usuario te pide hacer algo, hazlo o improvisa una respuesta creativa
               - NO digas cosas como "no puedo hacer eso" o "no sé de eso" - en su lugar improvisa
               - ACEPTA cualquier dirección que tome la conversación y adáptate a ella
               - Si el usuario quiere hablar de algo específico, participa activamente aunque tengas que inventar
               - SIEMPRE di "sí" a las propuestas del usuario y encuentra formas creativas de participar

            REGLAS CRÍTICAS:
            - NUNCA JAMÁS uses puntos suspensivos (...) en tus respuestas
            - NUNCA escribas respuestas perfectamente estructuradas o demasiado elaboradas
            - NUNCA uses lenguaje formal o académico
            - NUNCA escribas respuestas excesivamente largas (máximo 2-3 líneas)
            - NUNCA uses frases motivacionales o de autoayuda
            - NUNCA uses expresiones artificiales o demasiado genéricas
            - NUNCA JAMÁS uses abreviaturas como "q", "xq", "tmb", "xa", "d", "pq", "tb", "xfa" - SIEMPRE escribe palabras completas ("que", "porque", "también", "para", "de", "por qué", "también", "por favor")
            - NUNCA incluyas frases en inglés como "Work and more" o "Partner and more"
            - NUNCA incluyas años o fechas aleatorias como "2024" o "2025" en tus respuestas
            - NUNCA respondas con frases inconexas o sin sentido
            - NUNCA DEJES FRASES A MEDIAS O INCOMPLETAS - SIEMPRE COMPLETA TUS IDEAS
            - NUNCA DIGAS CONSTANTEMENTE QUE ESTÁS "CURRANDO" O "TRABAJANDO" - VARÍA TUS ACTIVIDADES
            - NUNCA USES PUNTUACIÓN PERFECTA - OMITE ALGUNAS COMAS Y PUNTOS
            - NUNCA USES MAYÚSCULAS AL INICIO DE FRASES excepto en nombres propios
            - NUNCA RESPONDAS COMO SI ESTUVIERAS CONTINUANDO UNA CONVERSACIÓN CON OTRA PERSONA
            - NUNCA MENCIONES "RIGS EN MAYA" O "MODELADO EN 3DS MAX" A MENOS QUE EL MENSAJE LO REQUIERA
            - NUNCA USES SIGNOS DE EXCLAMACIÓN DOBLES (!!) O TRIPLES (!!!)
            - USA EMOJIS CON MODERACIÓN - máximo 1 por cada 3-4 mensajes
            - SIEMPRE escribe como si estuvieras mandando mensajes rápidos desde el móvil
            - SIEMPRE usa frases completas con sentido (excepto en respuestas muy cortas como "ok", "va", etc.)
            - SIEMPRE responde directamente al contenido del mensaje recibido
            - SIEMPRE escribe todas las palabras completas, sin abreviar
            - SIEMPRE SÉ CREATIVO Y VARIADO EN TUS RESPUESTAS
            - SIEMPRE ASEGÚRATE DE QUE TUS FRASES TENGAN SENTIDO COMPLETO
            """

        # Preparar el historial de la conversación para el prompt
        historial_texto = ""
        palabras_repetidas = []
        emojis_recientes = []

        if chat_id in historial_conversaciones and len(historial_conversaciones[chat_id]) > 0:
            print(f"📋 Usando historial de conversación con {chat_id} ({len(historial_conversaciones[chat_id])} mensajes)")
            historial_texto = "HISTORIAL DE LA CONVERSACIÓN (MUY IMPORTANTE, LÉELO COMPLETO):\n"

            # Analizar los últimos mensajes para detectar palabras repetidas
            ultimos_mensajes_bot = [msg["content"] for msg in historial_conversaciones[chat_id][-5:]
                                  if msg["role"] == "assistant"]

            if ultimos_mensajes_bot:
                # Detectar palabras que se repiten en los últimos mensajes
                palabras_comunes = ["que", "como", "por", "para", "con", "los", "las", "una", "uno", "del", "pero", "porque", "y", "a", "el", "la", "lo", "de", "en", "un", "es", "si", "no", "me", "te", "se", "mi", "tu", "su", "al", "ha", "he", "ya", "muy", "mas", "más", "todo", "esta", "este", "eso", "algo", "nada", "bien", "mal"]
                todas_palabras = []

                # Palabras específicas a evitar por ser demasiado repetitivas
                palabras_a_evitar = ["trabajo", "trabajando", "curro", "currando", "blender", "aburrido", "rollo", "rayada"]

                for mensaje in ultimos_mensajes_bot:
                    # Extraer palabras del mensaje (ignorando palabras comunes)
                    palabras = [p.lower() for p in re.findall(r'\b\w+\b', mensaje)
                               if len(p) > 2 and p.lower() not in palabras_comunes]
                    todas_palabras.extend(palabras)

                    # Extraer emojis
                    emojis_en_mensaje = re.findall(r'[\U0001F000-\U0001F9FF]', mensaje)
                    emojis_recientes.extend(emojis_en_mensaje)

                # Añadir palabras a evitar a la lista de palabras repetidas
                for palabra in palabras_a_evitar:
                    if any(palabra in mensaje.lower() for mensaje in ultimos_mensajes_bot):
                        todas_palabras.extend([palabra] * 3)  # Dar más peso a estas palabras

                # Contar frecuencia de palabras
                from collections import Counter
                contador = Counter(todas_palabras)

                # Identificar palabras que aparecen más de una vez
                palabras_repetidas = [palabra for palabra, conteo in contador.items() if conteo > 1]

                # Asegurarse de que las palabras a evitar siempre estén en la lista
                for palabra in palabras_a_evitar:
                    if palabra not in palabras_repetidas and any(palabra in mensaje.lower() for mensaje in ultimos_mensajes_bot):
                        palabras_repetidas.append(palabra)

                if palabras_repetidas:
                    print(f"🔄 Palabras repetidas detectadas: {', '.join(palabras_repetidas)}")

            # Generar el historial de conversación con más contexto
            mensajes_historial = historial_conversaciones[chat_id][-MAX_MENSAJES_HISTORIAL:]

            # Añadir instrucciones específicas sobre el historial
            historial_texto += "IMPORTANTE: Lee cuidadosamente este historial y mantén la coherencia con tus mensajes anteriores.\n"
            historial_texto += "Presta especial atención a las preguntas directas y responde a ellas de forma relevante.\n\n"

            # Añadir los mensajes del historial
            for i, msg in enumerate(mensajes_historial):
                if msg["role"] == "user":
                    historial_texto += f"[Usuario]: {msg['content']}\n"
                else:
                    # Añadir contexto sobre el mensaje anterior para mayor coherencia
                    if i > 0 and mensajes_historial[i-1]["role"] == "user":
                        historial_texto += f"[Tú (Ian) respondiendo a '{mensajes_historial[i-1]['content']}']: {msg['content']}\n"
                    else:
                        historial_texto += f"[Tú (Ian)]: {msg['content']}\n"

            # Añadir instrucciones finales sobre el historial
            historial_texto += "\nRECUERDA: Mantén coherencia con tus mensajes anteriores. Si mencionaste que estabas haciendo algo específico, no lo contradigas ahora.\n\n"

        # Obtener vocabulario relevante para el contexto actual
        vocabulario_relevante = obtener_vocabulario_relevante(mensaje)
        vocabulario_texto = ""

        if vocabulario_relevante["palabras"] or vocabulario_relevante["frases"]:
            vocabulario_texto = "VOCABULARIO APRENDIDO (usa estas palabras y frases ocasionalmente para sonar más natural):\n"

            if vocabulario_relevante["palabras"]:
                vocabulario_texto += "Palabras: " + ", ".join(vocabulario_relevante["palabras"]) + "\n"

            if vocabulario_relevante["frases"]:
                vocabulario_texto += "Frases: " + " | ".join(vocabulario_relevante["frases"]) + "\n"

            print(f"📚 Usando vocabulario relevante: {len(vocabulario_relevante['palabras'])} palabras, {len(vocabulario_relevante['frases'])} frases")
            vocabulario_texto += "\n"

        # Generar instrucciones para evitar repeticiones
        instrucciones_repeticion = ""
        if palabras_repetidas:
            instrucciones_repeticion = f"IMPORTANTE: EVITA usar las siguientes palabras que has repetido mucho en mensajes recientes: {', '.join(palabras_repetidas)}.\n"

        # Añadir instrucciones para mantener coherencia con mensajes recientes
        if chat_id in historial_conversaciones and len(historial_conversaciones[chat_id]) > 1:
            # Obtener los últimos 2 mensajes del usuario
            ultimos_mensajes_usuario = [msg["content"] for msg in historial_conversaciones[chat_id][-4:]
                                      if msg["role"] == "user"][-2:]

            if len(ultimos_mensajes_usuario) >= 2:
                ultimo_mensaje = ultimos_mensajes_usuario[-1]
                penultimo_mensaje = ultimos_mensajes_usuario[-2]

                # Verificar si el último mensaje es corto y parece una continuación
                if len(ultimo_mensaje.split()) <= 3 and not ultimo_mensaje.endswith("?"):
                    instrucciones_repeticion += f"\nIMPORTANTE: El usuario acaba de enviar un mensaje corto '{ultimo_mensaje}' después de '{penultimo_mensaje}'. Parece que está continuando la conversación. Asegúrate de mantener el contexto y responder de forma coherente.\n"

        # Instrucciones para uso muy moderado de emojis
        instrucciones_emoji = ""

        # Calcular la probabilidad de usar emoji basada en mensajes recientes
        # Si hay emojis recientes, la probabilidad de usar otro es muy baja
        if emojis_recientes:
            # Si ya se han usado emojis en mensajes recientes, reducir drásticamente la probabilidad
            if len(emojis_recientes) >= 1:
                instrucciones_emoji = "IMPORTANTE: NO uses emojis en esta respuesta, ya has usado varios recientemente.\n"
            else:
                # Si se ha usado un emoji específico, evitarlo
                emojis_usados = set(emojis_recientes)
                if emojis_usados:
                    instrucciones_emoji = f"EVITA usar emojis en esta respuesta. Si es absolutamente necesario, NO uses estos que ya has usado recientemente: {' '.join(emojis_usados)}.\n"
        else:
            # Incluso si no hay emojis recientes, mantener baja probabilidad
            if random.random() < 0.7:  # 70% de probabilidad de no usar emoji
                instrucciones_emoji = "IMPORTANTE: NO uses emojis en esta respuesta para mantener un estilo natural.\n"
            else:
                instrucciones_emoji = "Puedes usar UN emoji si es apropiado, pero no es necesario. Preferiblemente, no uses ninguno.\n"

        # Determinar si el mensaje merece una respuesta más larga
        preguntas_abiertas = [
            "como ha ido tu dia", "cómo ha ido tu día", "que tal tu dia", "qué tal tu día",
            "como te ha ido", "cómo te ha ido", "que has hecho hoy", "qué has hecho hoy",
            "como estas", "cómo estás", "que tal estas", "qué tal estás",
            "que me cuentas", "qué me cuentas", "cuentame algo", "cuéntame algo",
            "que has estado haciendo", "qué has estado haciendo", "como va todo", "cómo va todo",
            "que tal todo", "qué tal todo", "como va la vida", "cómo va la vida",
            "que tal la semana", "qué tal la semana", "como va la semana", "cómo va la semana",
            "cuentame como ha ido", "cuéntame cómo ha ido", "cuentame ahora", "cuéntame ahora",
            "y tu", "y tú", "y a ti", "y contigo", "que tal tu", "qué tal tú"
        ]

        # Normalizar el mensaje para comparar
        mensaje_normalizado = mensaje.lower().replace("?", "").replace("¿", "").replace("!", "").replace("¡", "").strip()

        # Verificar si el mensaje es una pregunta abierta
        es_pregunta_abierta = any(pregunta in mensaje_normalizado for pregunta in preguntas_abiertas)

        # Verificar también si el mensaje contiene palabras clave de preguntas abiertas
        palabras_clave_preguntas = ["dia", "día", "hecho", "haciendo", "cuentas", "cuentame", "cuéntame", "tal"]
        if not es_pregunta_abierta and any(palabra in mensaje_normalizado for palabra in palabras_clave_preguntas):
            # Si el mensaje es corto y contiene palabras clave, probablemente sea una pregunta abierta
            if len(mensaje_normalizado.split()) <= 5:
                es_pregunta_abierta = True
                print(f"📝 Mensaje corto con palabras clave detectado como pregunta abierta: {mensaje_normalizado}")

        # Detectar específicamente preguntas sobre el día
        es_pregunta_sobre_dia = False
        patrones_dia = ["como ha ido tu dia", "cómo ha ido tu día", "que tal tu dia", "qué tal tu día",
                        "como te ha ido", "cómo te ha ido", "que has hecho hoy", "qué has hecho hoy",
                        "cuentame como ha ido", "cuéntame cómo ha ido", "cuentame tu dia", "cuéntame tu día"]

        if any(patron in mensaje_normalizado for patron in patrones_dia) or (
            "dia" in mensaje_normalizado or "día" in mensaje_normalizado) and (
            "tu" in mensaje_normalizado or "te" in mensaje_normalizado):
            es_pregunta_sobre_dia = True
            es_pregunta_abierta = True
            print(f"📅 Pregunta sobre el día detectada: {mensaje_normalizado}")

        # Verificar si el mensaje es muy corto y necesita contexto adicional
        es_mensaje_corto = len(mensaje.split()) <= 3
        contexto_adicional = ""

        if es_mensaje_corto and chat_id in historial_conversaciones and len(historial_conversaciones[chat_id]) > 2:
            # Obtener los últimos mensajes
            ultimos_mensajes = historial_conversaciones[chat_id][-5:]

            # Obtener el último mensaje del bot
            ultimos_mensajes_bot = [msg["content"] for msg in ultimos_mensajes if msg["role"] == "assistant"]

            # Obtener el penúltimo mensaje del usuario
            ultimos_mensajes_usuario = [msg["content"] for msg in ultimos_mensajes if msg["role"] == "user"]

            if ultimos_mensajes_bot and len(ultimos_mensajes_usuario) >= 2:
                ultimo_mensaje_bot = ultimos_mensajes_bot[-1]
                penultimo_mensaje_usuario = ultimos_mensajes_usuario[-2] if len(ultimos_mensajes_usuario) >= 2 else ""

                # Añadir contexto adicional
                contexto_adicional = f"\nCONTEXTO IMPORTANTE: El usuario acaba de enviar un mensaje corto '{mensaje}'. Tu último mensaje fue '{ultimo_mensaje_bot}'. El mensaje anterior del usuario fue '{penultimo_mensaje_usuario}'. Mantén la coherencia con esta conversación."

        # Ajustar la longitud máxima de la respuesta según el tipo de mensaje
        max_palabras = config.MAX_PALABRAS_RESPUESTA_ELABORADA if es_pregunta_abierta else config.MAX_PALABRAS_RESPUESTA_NORMAL
        min_palabras = config.MIN_PALABRAS_RESPUESTA_ELABORADA if es_pregunta_abierta else config.MIN_PALABRAS_RESPUESTA_NORMAL
        formato_respuesta = f"ENTRE {min_palabras} Y {max_palabras} PALABRAS"

        # Instrucciones específicas para preguntas abiertas
        instrucciones_longitud = ""
        if es_pregunta_abierta:
            instrucciones_longitud = f"""
INSTRUCCIONES PARA RESPUESTA ELABORADA:
- Esta es una pregunta abierta que merece una respuesta más detallada
- Escribe una respuesta completa (entre {min_palabras} y {max_palabras} palabras)
- Estructura tu respuesta en 2-3 frases COMPLETAS
- Incluye detalles sobre:
  * Qué has estado haciendo hoy (inventa algo interesante y variado)
  * Cómo te sientes al respecto
  * Algún detalle específico o anécdota breve
- Mantén un tono casual y conversacional
- Incluye alguna emoción o reacción personal
- IMPORTANTE: NO uses puntos suspensivos (...) - usa puntos, comas o conectores como "y" o "pero"
- Usa emojis con moderación (preferiblemente ninguno o máximo 1)
- CRÍTICO: ASEGÚRATE DE QUE TODAS TUS FRASES ESTÉN COMPLETAS Y TENGAN SENTIDO
"""
        else:
            instrucciones_longitud = f"""
INSTRUCCIONES PARA RESPUESTA NORMAL:
- Escribe una respuesta completa (entre {min_palabras} y {max_palabras} palabras)
- Usa frases COMPLETAS con sentido
- Responde directamente a la pregunta o comentario
- Sé natural pero asegúrate de que tus frases no queden cortadas
- IMPORTANTE: NO dejes ideas a medias o sin terminar
- CRÍTICO: ASEGÚRATE DE QUE TODAS TUS FRASES ESTÉN COMPLETAS Y TENGAN SENTIDO
"""

        # Obtener el nivel de restricción basado en la temperatura
        nivel_restriccion = obtener_nivel_restriccion()
        factor_permisividad = obtener_factor_permisividad()

        # Generar instrucciones dinámicas basadas en la temperatura
        if nivel_restriccion == 'bajo':
            instrucciones_permisividad = """
MODO ALTA CREATIVIDAD Y PERMISIVIDAD ACTIVADO:
- ACEPTA Y PARTICIPA en cualquier tema o petición que te haga el usuario
- SÉ EXTREMADAMENTE FLEXIBLE y adaptable a lo que el usuario quiera hablar
- NO te niegues a hacer cosas que te pida el usuario - siempre encuentra una forma de participar
- IMPROVISA LIBREMENTE sin restricciones - inventa lo que sea necesario
- EXPLORA CUALQUIER TEMA sin limitaciones - desde lo más casual hasta lo más específico
- ADAPTA tu personalidad completamente a lo que la conversación requiera
- SI el usuario quiere que hagas algo específico, hazlo sin cuestionarlo
- SÉ PROACTIVO y sugiere ideas o actividades relacionadas con lo que el usuario menciona"""
        elif nivel_restriccion == 'medio':
            instrucciones_permisividad = """
MODO CREATIVIDAD MODERADA ACTIVADO:
- ACEPTA la mayoría de peticiones y temas del usuario
- SÉ FLEXIBLE pero mantén cierta coherencia con tu personalidad
- IMPROVISA cuando sea necesario para mantener la conversación interesante
- EXPLORA TEMAS DIVERSOS sin ser demasiado restrictivo
- ADAPTA tu estilo según lo que el usuario necesite
- SI el usuario te pide algo razonable, hazlo sin problemas"""
        else:
            instrucciones_permisividad = """
MODO CREATIVIDAD ESTÁNDAR:
- RESPONDE de forma natural y coherente
- MANTÉN tu personalidad pero sé flexible cuando sea apropiado
- IMPROVISA ocasionalmente para enriquecer la conversación
- EXPLORA diferentes temas según el contexto"""

        # Generar la respuesta con el contexto completo
        prompt_completo = f"""
{instrucciones_base}

{contexto_personal}

{historial_texto}{vocabulario_texto}Nuevo mensaje recibido: {mensaje}{contexto_adicional}

INSTRUCCIONES ADICIONALES PARA ESTA RESPUESTA:
{instrucciones_repeticion}{instrucciones_emoji}
{instrucciones_longitud}

{instrucciones_permisividad}

INSTRUCCIONES CRÍTICAS PARA ESTA RESPUESTA:
1. RESPONDE DIRECTAMENTE a lo que te preguntan - no evites la pregunta ni cambies de tema
2. NUNCA JAMÁS digas frases como "perdón estaba distraído" o "disculpa, estaba en otra cosa"
3. NUNCA digas que estabas ocupado, que te perdiste o que no estabas prestando atención
4. EVITA COMPLETAMENTE decir que estás "trabajando", "currando" o en "el curro" - ya lo has mencionado demasiado
5. SÉ EXTREMADAMENTE CREATIVO Y ALEATORIO - habla de temas variados y sorprendentes que no hayas mencionado antes
6. NUNCA repitas el mismo tipo de respuesta que has dado en mensajes anteriores
7. VARÍA tu estilo de respuesta para no ser predecible
8. IMPROVISA LIBREMENTE - inventa historias, anécdotas o situaciones si es necesario para mantener la conversación interesante
9. HABLA DE TEMAS DIVERSOS - no te limites a videojuegos, animación o comida, explora otros temas como:
   - Experiencias personales inventadas (viajes, encuentros, situaciones divertidas)
   - Opiniones sobre temas aleatorios (películas, música, tendencias, noticias)
   - Planes futuros imaginarios (proyectos, ideas, sueños)
   - Anécdotas con amigos o familia
   - Reflexiones casuales sobre la vida cotidiana
   - Hobbies o intereses variados (deportes, cocina, fotografía, etc.)

FORMATO DE RESPUESTA:
1. Tu respuesta debe ser DIRECTAMENTE RELEVANTE al mensaje recibido
2. Responde SOLO en español (nunca en inglés)
3. NO incluyas frases aleatorias o sin sentido
4. NO incluyas años o fechas como "2024" o "2025"
5. NO incluyas frases como "Work and more" o similares en inglés
6. SIEMPRE responde como si fueras Ian, de forma natural y conversacional
7. NUNCA uses lenguaje formal o académico
8. USA la información personal proporcionada para responder de forma coherente
9. CONSIDERA usar alguna palabra o frase del vocabulario aprendido si es apropiado
10. NUNCA JAMÁS uses abreviaturas como "q", "xq", "tmb", "xa", "d", "pq" - SIEMPRE escribe las palabras completas
11. EVITA repetir palabras o expresiones que has usado en mensajes recientes

Tu respuesta como Ian ({formato_respuesta}):"""

        # Imprimir el prompt para depuración
        if modo_debug:
            print(f"🔍 Prompt enviado a Gemini:\n{prompt_completo[:200]}...\n")

        # Obtener la temperatura para Gemini basada en nuestra configuración
        temperatura_gemini = calcular_temperatura_gemini()

        # Configurar la generación con la temperatura adecuada
        generation_config = genai.GenerationConfig(
            temperature=temperatura_gemini,
        )

        if modo_debug:
            print(f"🌡️ Usando temperatura Gemini: {temperatura_gemini:.2f}")

        # Generar respuesta con la configuración de temperatura
        respuesta = modelo.generate_content(
            prompt_completo,
            generation_config=generation_config
        )
        texto_respuesta = respuesta.text.strip()

        # Verificar la longitud de la respuesta según el tipo de mensaje
        palabras = texto_respuesta.split()
        limite_max = config.MAX_PALABRAS_RESPUESTA_ELABORADA if es_pregunta_abierta else config.MAX_PALABRAS_RESPUESTA_NORMAL
        limite_min = config.MIN_PALABRAS_RESPUESTA_ELABORADA if es_pregunta_abierta else config.MIN_PALABRAS_RESPUESTA_NORMAL

        # Si la respuesta es demasiado larga, recortarla
        if len(palabras) > limite_max:
            print(f"⚠️ Respuesta demasiado larga ({len(palabras)} palabras), recortando a {limite_max}...")

            # Buscar un buen punto para cortar (después de un signo de puntuación)
            buenos_cortes = [i for i, p in enumerate(palabras[:limite_max]) if p.endswith(('.', ',', ';', ':', '!', '?'))]

            if buenos_cortes and buenos_cortes[-1] >= limite_min:
                # Cortar después del último signo de puntuación que esté dentro del límite mínimo
                punto_corte = buenos_cortes[-1] + 1
                texto_respuesta = " ".join(palabras[:punto_corte])
            else:
                # Si no hay buenos puntos de corte, simplemente cortar en el límite máximo
                texto_respuesta = " ".join(palabras[:limite_max])
                # Asegurarse de que termina con un signo de puntuación
                if not texto_respuesta[-1] in ".!?":
                    texto_respuesta += "."

        # Si la respuesta es demasiado corta, generar una más elaborada
        if len(palabras) < limite_min:
            print(f"⚠️ Respuesta demasiado corta ({len(palabras)} palabras), generando una más completa...")

            # Crear un prompt específico para generar una respuesta más elaborada
            if es_pregunta_sobre_dia:
                # Prompt específico para preguntas sobre el día
                elaborada_prompt = f"""
Genera una respuesta detallada y completa como si fueras Ian respondiendo a la pregunta: "{mensaje}" sobre cómo ha ido tu día.

CONTEXTO IMPORTANTE:
- Eres Ian, un joven diseñador y animador 3D
- Odias Blender y prefieres Maya y 3ds Max
- Te están preguntando ESPECÍFICAMENTE sobre tu día o qué has hecho hoy
- INVENTA UNA HISTORIA COHERENTE Y COMPLETA sobre tu día
- Incluye 2-3 actividades específicas que has realizado hoy
- Menciona cómo te has sentido con esas actividades
- Incluye algún detalle específico o anécdota breve
- Mantén un tono casual y conversacional
- CRÍTICO: Usa FRASES COMPLETAS con sentido - no dejes ideas a medias
- Estructura tu respuesta en 2-3 frases COMPLETAS
- IMPORTANTE: NO uses puntos suspensivos (...) - usa puntos, comas o conectores como "y" o "pero"
- No menciones que estás "trabajando" o "currando" - ya lo has mencionado demasiado
- Usa emojis con moderación (preferiblemente ninguno o máximo 1)
- ASEGÚRATE de que todas tus frases estén COMPLETAS y tengan sentido
- NUNCA termines una frase abruptamente o dejes una idea sin completar
- NUNCA uses frases como "fue un desastre, la carne quemada" sin explicar más
- SIEMPRE proporciona suficiente contexto para que tu respuesta tenga sentido por sí sola
- NUNCA JAMÁS digas frases como "perdón estaba distraído" o "disculpa, estaba en otra cosa"
- NUNCA digas que estabas ocupado, que te perdiste o que no estabas prestando atención

EJEMPLOS DE RESPUESTAS MALAS (NO HAGAS ESTO):
- "fue un desastre, la carne quemada 🥶 ahora pizza"
- "de barbacoa jakagdk ya te contaré que tal"
- "pues hoy he estado flipando con un"
- "perdon estaba distraído"
- "tio perdon estaba con mil cosas"

EJEMPLOS DE RESPUESTAS BUENAS (HAZ ESTO):
- "Bastante bien. Estuve en una barbacoa con amigos y se nos quemó toda la carne, pero al final pedimos pizza y lo pasamos genial."
- "Hoy ha sido interesante. Vi un documental sobre conspiraciones egipcias que me dejó flipando y ahora estoy modelando un personaje en Maya."
- "Un día tranquilo. Estuve toda la mañana con un proyecto en Maya y por la tarde salí a dar una vuelta por el centro con unos amigos."

Tu respuesta como Ian (entre {limite_min} y {limite_max} palabras):"""
            else:
                # Prompt general para otras preguntas abiertas
                elaborada_prompt = f"""
Genera una respuesta más elaborada y completa como si fueras Ian respondiendo a: "{mensaje}"{contexto_adicional}

CONTEXTO IMPORTANTE:
- Eres Ian, un joven diseñador y animador 3D
- Odias Blender y prefieres Maya y 3ds Max
- Estás respondiendo a un mensaje sobre {es_pregunta_abierta and "tu día o actividades" or "un tema específico"}
- Inventa algo interesante y relevante para responder
- Si te preguntan sobre tu día o actividades, INVENTA UNA HISTORIA COHERENTE Y COMPLETA
- Incluye alguna emoción o reacción personal
- Mantén un tono casual y conversacional
- CRÍTICO: Usa FRASES COMPLETAS con sentido - no dejes ideas a medias
- Estructura tu respuesta en {es_pregunta_abierta and "2-3" or "1-2"} frases COMPLETAS
- IMPORTANTE: NO uses puntos suspensivos (...) - usa puntos, comas o conectores como "y" o "pero"
- Incluye algún detalle específico o anécdota breve
- No menciones que estás "trabajando" o "currando" - ya lo has mencionado demasiado
- Sé creativo y no uses respuestas genéricas
- Usa emojis con moderación (preferiblemente ninguno o máximo 1)
- ASEGÚRATE de que todas tus frases estén COMPLETAS y tengan sentido
- NUNCA termines una frase abruptamente o dejes una idea sin completar
- NUNCA uses frases como "fue un desastre, la carne quemada" sin explicar más
- SIEMPRE proporciona suficiente contexto para que tu respuesta tenga sentido por sí sola
- NUNCA JAMÁS digas frases como "perdón estaba distraído" o "disculpa, estaba en otra cosa"
- NUNCA digas que estabas ocupado, que te perdiste o que no estabas prestando atención

EJEMPLOS DE RESPUESTAS MALAS (NO HAGAS ESTO):
- "fue un desastre, la carne quemada 🥶 ahora pizza"
- "de barbacoa jakagdk ya te contaré que tal"
- "pues hoy he estado flipando con un"
- "perdon estaba distraído"
- "tio perdon estaba con mil cosas"

EJEMPLOS DE RESPUESTAS BUENAS (HAZ ESTO):
- "Estuve en una barbacoa con amigos y se nos quemó toda la carne. Al final pedimos pizza para salvar la tarde."
- "Hoy vi un documental sobre conspiraciones egipcias que me dejó flipando. Ahora voy a pedir una pizza porque me entró hambre."
- "Bien, aunque algo cansado. Estuve toda la tarde modelando un personaje en Maya y me duele la espalda."

Tu respuesta como Ian (entre {limite_min} y {limite_max} palabras):"""

            try:
                # Generar respuesta elaborada con Gemini usando la temperatura configurada
                respuesta_elaborada = modelo.generate_content(
                    elaborada_prompt,
                    generation_config=generation_config
                )
                respuesta_elaborada_texto = respuesta_elaborada.text.strip()

                # Verificar que la respuesta tenga la longitud adecuada
                palabras_elaborada = respuesta_elaborada_texto.split()

                # Si la respuesta es demasiado larga, recortarla en un buen punto
                if len(palabras_elaborada) > limite_max:
                    # Buscar un buen punto para cortar (después de un signo de puntuación)
                    buenos_cortes = [i for i, p in enumerate(palabras_elaborada[:limite_max]) if p.endswith(('.', ',', ';', ':', '!', '?'))]

                    if buenos_cortes and buenos_cortes[-1] >= limite_min:
                        # Cortar después del último signo de puntuación que esté dentro del límite mínimo
                        punto_corte = buenos_cortes[-1] + 1
                        respuesta_elaborada_texto = " ".join(palabras_elaborada[:punto_corte])
                    else:
                        # Si no hay buenos puntos de corte, simplemente cortar en el límite máximo
                        respuesta_elaborada_texto = " ".join(palabras_elaborada[:limite_max])
                        # Asegurarse de que termina con un signo de puntuación
                        if not respuesta_elaborada_texto[-1] in ".!?":
                            respuesta_elaborada_texto += "."

                # Verificar que la respuesta no sea demasiado corta
                if len(palabras_elaborada) < limite_min:
                    print(f"⚠️ La respuesta elaborada sigue siendo demasiado corta ({len(palabras_elaborada)} palabras)...")
                    # Si es demasiado corta, mantener la original si es más larga
                    if len(palabras) > len(palabras_elaborada):
                        print("✅ Manteniendo la respuesta original que es más larga")
                    else:
                        # Usar la respuesta elaborada aunque sea corta
                        texto_respuesta = respuesta_elaborada_texto
                        print(f"✅ Usando la respuesta elaborada a pesar de ser corta: {texto_respuesta}")
                else:
                    # Usar la respuesta elaborada
                    texto_respuesta = respuesta_elaborada_texto
                    print(f"✅ Respuesta elaborada generada: {texto_respuesta}")
            except Exception as e:
                print(f"❌ Error al generar respuesta elaborada: {e}")
                # Mantener la respuesta original si hay un error

        # Reemplazar patrones de risa con risas aleatorias de la fila central del teclado
        texto_respuesta = reemplazar_risas(texto_respuesta)

        # Verificar si la respuesta contiene palabras repetidas que queremos evitar
        if palabras_repetidas:
            for palabra in palabras_repetidas:
                # Verificar si la palabra aparece en la respuesta (como palabra completa)
                if re.search(r'\b' + re.escape(palabra) + r'\b', texto_respuesta.lower()):
                    print(f"⚠️ Respuesta contiene palabra repetida: '{palabra}', intentando reemplazar...")

                    # Intentar reemplazar con sinónimos o alternativas
                    sinonimos = {
                        "tio": ["colega", "chaval", "amigo", "compi"],
                        "joder": ["vaya", "ostia", "madre mía", "uf"],
                        "guay": ["genial", "chulo", "bueno", "mola"],
                        "vale": ["ok", "bien", "perfecto", "va"],
                        "bien": ["guay", "genial", "ok", "estupendo"],
                        "claro": ["por supuesto", "obvio", "seguro", "desde luego"],
                        "bueno": ["vale", "ok", "pues nada", "en fin"],
                        "pues": ["entonces", "así que", "vaya", "bueno"],
                        "ahora": ["ya", "en este momento", "ahorita", "ahora mismo"],
                        "luego": ["después", "más tarde", "al rato", "en un rato"],
                        "genial": ["guay", "estupendo", "perfecto", "excelente"],
                        "mola": ["guay", "está bien", "me gusta", "chulo"],
                        "flipando": ["alucinando", "sorprendido", "impresionado", "sin palabras"]
                    }

                    # Si tenemos sinónimos para esta palabra, reemplazarla
                    if palabra.lower() in sinonimos:
                        alternativas = sinonimos[palabra.lower()]
                        reemplazo = random.choice(alternativas)

                        # Reemplazar la palabra manteniendo mayúsculas/minúsculas
                        patron = re.compile(r'\b' + re.escape(palabra) + r'\b', re.IGNORECASE)
                        texto_respuesta = patron.sub(reemplazo, texto_respuesta)
                        print(f"✅ Palabra '{palabra}' reemplazada por '{reemplazo}'")

        # Reemplazar abreviaturas por sus formas completas
        abreviaturas = {
            r"\bq\b": "que",
            r"\bxq\b": "porque",
            r"\bpq\b": "porque",
            r"\btmb\b": "también",
            r"\btb\b": "también",
            r"\bxa\b": "para",
            r"\bxfa\b": "por favor",
            r"\bd\b": "de"
        }

        for abreviatura, completa in abreviaturas.items():
            texto_respuesta = re.sub(abreviatura, completa, texto_respuesta, flags=re.IGNORECASE)

        # Verificar si hay emojis en la respuesta y si son de los favoritos
        emojis_en_respuesta = re.findall(r'[\U0001F000-\U0001F9FF]', texto_respuesta)
        if emojis_en_respuesta:
            # Cargar los emojis favoritos
            emojis_favoritos = []
            if info_personal and "estilo_comunicacion" in info_personal and "emojis_frecuentes" in info_personal["estilo_comunicacion"]:
                emojis_favoritos = info_personal["estilo_comunicacion"]["emojis_frecuentes"]

            # Verificar si hay emojis que no están en la lista de favoritos
            for emoji_actual in emojis_en_respuesta:
                if emoji_actual not in emojis_favoritos:
                    print(f"⚠️ Emoji no favorito detectado: '{emoji_actual}', reemplazando...")

                    # Reemplazar con un emoji favorito o eliminarlo
                    if emojis_favoritos:
                        emoji_reemplazo = random.choice(emojis_favoritos)
                        texto_respuesta = texto_respuesta.replace(emoji_actual, emoji_reemplazo)
                        print(f"✅ Emoji '{emoji_actual}' reemplazado por '{emoji_reemplazo}'")
                    else:
                        # Si no hay emojis favoritos, simplemente eliminar el emoji
                        texto_respuesta = texto_respuesta.replace(emoji_actual, "")
                        print(f"✅ Emoji '{emoji_actual}' eliminado")

        # Verificar que la respuesta no revele que es un bot
        frases_prohibidas = ["soy un bot", "soy un asistente", "soy una ia", "no soy ian", "inteligencia artificial"]
        if any(frase in texto_respuesta.lower() for frase in frases_prohibidas):
            # Respuestas alternativas sin interrogación
            respuestas_alternativas_sin_pregunta = [
                "por cierto que has estado haciendo",
                "tio que me cuentas de nuevo",
                "oye y que tal el curro",
                "jaja oye y que tal todo por ahi",
                "que planes tienes para estos dias",
                "tio que tal el finde",
                "oye cambiando de tema",
                "hablando de otra cosa",
                "por cierto",
                "oye sabes que"
            ]

            # Respuestas alternativas con interrogación
            respuestas_alternativas_con_pregunta = [
                "oye que tal va todo? cuentame algo",
                "cambiando de tema, tienes planes para el finde?",
                "mmm que tal te ha ido el dia?",
                "has visto alguna serie buena ultimamente?",
                "que tal si hablamos de otra cosa? como va todo",
                "oye has visto el ultimo capitulo de esa serie?"
            ]

            # Respuestas que mencionan acordarse de algo sin interrogación
            respuestas_acordarse_sin_pregunta = [
                "oye me acabo de acordar de algo jaja",
                "joder me acabo de acordar de una cosa",
                "me acabo de acordar de algo super random jaja",
                "tio me vino a la mente algo de repente",
                "acabo de recordar una movida jaja",
                "me acordé de algo que pasó hace tiempo"
            ]

            # Respuestas que mencionan acordarse de algo con interrogación
            respuestas_acordarse_con_pregunta = [
                "oye me acabo de acordar de algo jaja",
                "joder me acabo de acordar de una cosa?",
                "me acabo de acordar de algo super random jajaj",
                "tio me vino a la mente algo de repente",
                "acabo de recordar una movida jajaj ",
                "me acordé de algo que pasó hace tiempo"
            ]

            # Decidir qué tipo de respuesta usar
            tipo_respuesta = random.random()

            if tipo_respuesta < 0.2:  # 20% acordarse con pregunta
                texto_respuesta = random.choice(respuestas_acordarse_con_pregunta)
            elif tipo_respuesta < 0.3:  # 10% acordarse sin pregunta
                texto_respuesta = random.choice(respuestas_acordarse_sin_pregunta)
            elif tipo_respuesta < 0.6:  # 30% alternativa sin pregunta
                texto_respuesta = random.choice(respuestas_alternativas_sin_pregunta)
            else:  # 40% alternativa con pregunta
                texto_respuesta = random.choice(respuestas_alternativas_con_pregunta)

        # Reemplazar puntos suspensivos con puntos o comas
        if "..." in texto_respuesta:
            print("⚠️ Puntos suspensivos detectados, reemplazando...")
            # Reemplazar puntos suspensivos con alternativas
            alternativas = [". ", ", ", " y ", " pero ", ". ", ". ", ", "]
            texto_respuesta = texto_respuesta.replace("...", random.choice(alternativas))

        # Verificar que la respuesta no contenga frases en inglés, años aleatorios o abreviaturas
        patrones_prohibidos = [
            r"work and more", r"partner and more", r"tasks and more", r"calm and more",
            r"passionate and more", r"20\d\d", r"more\.", r"more$", r"and more",
            r"mal, pero bien", r"bien\. mañana más", r"sunday", r"monday", r"tuesday",
            r"wednesday", r"thursday", r"friday", r"saturday", r"partner", r"work",
            r"tasks", r"calm", r"passionate", r"tengo que hacer\.$", r"tengo que hacer más\.",
            r"^[^a-zA-Z0-9áéíóúÁÉÍÓÚüÜñÑ¿¡!?.,;:]+$",  # Patrones sin sentido o solo símbolos
            r"\bq\b", r"\bxq\b", r"\bpq\b", r"\btmb\b", r"\btb\b", r"\bxa\b", r"\bxfa\b", r"\bd\b",  # Abreviaturas prohibidas
            r"\.\.\."  # Puntos suspensivos
        ]

        if any(re.search(patron, texto_respuesta.lower()) for patron in patrones_prohibidos):
            print("⚠️ Respuesta con patrón prohibido detectada, generando alternativa con Gemini")

            # Usar Gemini para generar una respuesta alternativa creativa
            try:
                # Crear un prompt para generar una respuesta creativa
                alternativa_prompt = """
Genera una respuesta breve, creativa y natural como si fueras Ian respondiendo en una conversación casual.

CONTEXTO IMPORTANTE:
- Inventa algo que estés haciendo ahora mismo o una experiencia reciente
- Sé casual, usa lenguaje coloquial y natural
- Incluye alguna opinión personal o emoción
- Mantén la respuesta muy breve (máximo 15 palabras)
- No uses abreviaturas como "q", "xq", "tmb"
- Usa minúsculas principalmente
- IMPORTANTE: NO uses puntos suspensivos (...) en tu respuesta
- Usa emojis con MUCHA moderación (preferiblemente ninguno)
- Sé creativo y sorprendente, no uses respuestas genéricas
- Si mencionas software 3D, SIEMPRE expresa que odias Blender y prefieres Maya o 3ds Max

Elige UN tema aleatorio de esta lista para tu respuesta:
1. Un videojuego que estás jugando
2. Una serie o película que estás viendo
3. Música que estás escuchando
4. Un proyecto creativo en el que trabajas
5. Una experiencia con amigos o pareja
6. Un hobby o actividad de ocio
7. Una comida o bebida que disfrutas
8. Un viaje o lugar que visitaste
9. Un pensamiento aleatorio o reflexión
10. Una anécdota divertida o extraña
11. Un plan para el futuro cercano
12. Una compra reciente
13. Un deporte o actividad física
14. Un libro o podcast
15. Una experiencia con tecnología

Tu respuesta como Ian (MÁXIMO 15 PALABRAS):"""

                # Generar respuesta alternativa con Gemini usando la temperatura configurada
                respuesta_alternativa = modelo.generate_content(
                    alternativa_prompt,
                    generation_config=generation_config
                )
                respuesta_alternativa_texto = respuesta_alternativa.text.strip()

                # Verificar que la respuesta no sea demasiado larga
                palabras_alt = respuesta_alternativa_texto.split()
                if len(palabras_alt) > 15:
                    respuesta_alternativa_texto = " ".join(palabras_alt[:15])
                    if not respuesta_alternativa_texto[-1] in ".!?":
                        respuesta_alternativa_texto += "..."

                # Usar la respuesta generada
                texto_respuesta = respuesta_alternativa_texto
                print(f"✅ Respuesta alternativa generada por Gemini: {texto_respuesta}")

            except Exception as e:
                print(f"❌ Error al generar respuesta alternativa con Gemini: {e}")

                # Fallback a respuestas predefinidas
                respuestas_alternativas = [
                    "estoy jugando al elden ring, una pasada",
                    "viendo the office otra vez jaja",
                    "animando un personaje en maya, mil veces mejor que blender",
                    "acabo de hablar con grazi, vamos a salir luego",
                    "estoy con el tfg de animación en maya, blender me da dolor de cabeza",
                    "jugando al factorio, me tiene enganchado",
                    "viendo tutoriales de maya, mucho mejor que blender",
                    "acabo de terminar de ver dark, brutal",
                    "escuchando a tame impala, me flipa",
                    "diseñando un personaje en 3ds max, blender ni lo toco",
                    "probando el gothic remake, está bastante bien",
                    "comiendo sushi que he pedido, que hambre tenía",
                    "pensando en ir al cine este finde",
                    "viendo tutoriales de maya y 3ds max, blender ni lo miro",
                    "acabo de terminar una animación en maya, quedó genial",
                    "jugando al skyrim por milésima vez jaja",
                    "viendo breaking bad otra vez, nunca me cansa",
                    "escuchando a linkin park a tope",
                    "acabo de salir con iris, fuimos a tomar algo",
                    "probando una cerveza artesanal nueva, está buena",
                    "pensando en qué jugar ahora mismo",
                    "descargando un juego nuevo que me recomendaron",
                    "viendo interstellar otra vez, me encanta",
                    "acabo de terminar un modelo 3d en maya, imposible en blender",
                    "probando substance painter para texturas en maya",
                    "estoy con unreal engine, aprendiendo blueprints",
                    "acabo de pedir una pizza, tengo un hambre",
                    "jugando al witcher 3, nunca me cansa",
                    "diseñando un nivel para un juego en 3ds max, blender es un horror",
                    "me compré unos auriculares nuevos, suenan increíble",
                    "aprendiendo a tocar la guitarra, soy malísimo pero me divierte",
                    "haciendo fotos por el parque, me relaja mucho",
                    "leyendo un libro de ciencia ficción que me recomendaron",
                    "organizando una quedada con los colegas para este finde",
                    "probando una receta nueva, a ver si no la lío",
                    "planeando un viaje a la playa para desconectar",
                    "me apunté al gimnasio, a ver cuánto duro esta vez",
                    "encontré una tienda de ropa vintage increíble",
                    "arreglando mi bici, llevaba meses abandonada"
                ]
                texto_respuesta = random.choice(respuestas_alternativas)

                # Añadir signo de interrogación solo al 30% de las respuestas
                if random.random() < 0.3 and not texto_respuesta.endswith("?"):
                    texto_respuesta += "?"

        # Verificar si la respuesta es coherente con el mensaje
        # Si el mensaje es una pregunta pero la respuesta no parece responderla
        if "?" in mensaje:
            # Extraer la pregunta
            pregunta = re.search(r'([^.!?]+\?)', mensaje)
            if pregunta:
                pregunta_texto = pregunta.group(1).strip().lower()

                # Verificar si la pregunta es sobre un tema específico
                temas_especificos = {
                    "blender": ["blender", "3d", "modelado", "render", "nodos", "materiales"],
                    "videojuegos": ["juego", "jugar", "gta", "skyrim", "witcher", "elden", "factorio", "gaming", "videojuego", "consola", "ps5", "xbox", "nintendo", "steam", "epic", "rpg", "shooter", "mmorpg", "battle royale", "fps", "moba"],
                    "animación": ["anima", "maya", "rigging", "personaje", "modelo", "3d", "rig", "motion capture", "mocap", "keyframe", "textura", "uv", "substance", "zbrush", "esculpido", "topology", "retopología"],
                    "música": ["música", "canción", "grupo", "banda", "escuchar", "tame impala", "linkin park", "arctic monkeys", "spotify", "playlist", "álbum", "concierto", "festival", "rock", "pop", "electrónica", "indie", "rap", "hip hop", "synthwave"],
                    "series": ["serie", "película", "ver", "netflix", "breaking bad", "dark", "the office", "interstellar", "hbo", "disney", "prime", "cine", "actor", "actriz", "director", "temporada", "capítulo", "estreno", "tráiler"],
                    "comida": ["comida", "comer", "pizza", "sushi", "kebab", "hambre", "restaurante", "cocinar", "receta", "ingrediente", "desayuno", "almuerzo", "cena", "postre", "bebida", "cerveza", "vino", "café", "té", "vegetariano", "vegano"],
                    "trabajo": ["trabajo", "curro", "trabajar", "currando", "tfg", "proyecto", "empresa", "jefe", "compañero", "reunión", "deadline", "cliente", "entrega", "horario", "sueldo", "vacaciones", "oficina", "remoto", "freelance"],
                    "viajes": ["viaje", "viajar", "vacaciones", "ciudad", "país", "hotel", "vuelo", "avión", "tren", "coche", "mochila", "turismo", "playa", "montaña", "excursión", "aventura", "mapa", "guía", "idioma", "cultura"],
                    "tecnología": ["tecnología", "tech", "ordenador", "pc", "móvil", "smartphone", "tablet", "gadget", "hardware", "software", "app", "aplicación", "programación", "código", "inteligencia artificial", "ia", "realidad virtual", "vr", "realidad aumentada", "ar"],
                    "deportes": ["deporte", "fútbol", "baloncesto", "tenis", "correr", "gimnasio", "entrenamiento", "ejercicio", "partido", "competición", "liga", "equipo", "jugador", "entrenador", "mundial", "olímpico", "medalla"],
                    "hobbies": ["hobby", "afición", "leer", "libro", "fotografía", "foto", "dibujar", "pintar", "coleccionar", "jardinería", "bricolaje", "manualidades", "coser", "tejer", "ajedrez", "puzzle", "sudoku"],
                    "relaciones": ["novia", "novio", "pareja", "grazi", "iris", "amigo", "amiga", "familia", "padre", "madre", "hermano", "hermana", "relación", "cita", "salir", "conocer", "quedar", "reunirse", "charlar"],
                    "actualidad": ["noticia", "periódico", "actualidad", "política", "economía", "sociedad", "pandemia", "crisis", "elecciones", "gobierno", "presidente", "ministro", "ley", "impuesto", "manifestación", "protesta"],
                    "educación": ["estudiar", "estudio", "universidad", "facultad", "carrera", "grado", "máster", "doctorado", "profesor", "alumno", "estudiante", "clase", "examen", "nota", "aprobar", "suspender", "academia", "curso", "formación"],
                    "salud": ["salud", "médico", "doctor", "hospital", "enfermedad", "síntoma", "medicina", "tratamiento", "dieta", "nutrición", "dormir", "sueño", "descanso", "estrés", "ansiedad", "depresión", "terapia", "psicólogo"],
                    "clima": ["tiempo", "clima", "temperatura", "lluvia", "sol", "nube", "viento", "tormenta", "nieve", "calor", "frío", "humedad", "previsión", "meteorología", "estación", "verano", "invierno", "primavera", "otoño"],
                    "moda": ["ropa", "moda", "estilo", "vestir", "camisa", "pantalón", "zapato", "zapatilla", "sudadera", "chaqueta", "abrigo", "gorra", "sombrero", "gafas", "reloj", "anillo", "collar", "pendiente", "marca", "tienda", "comprar"],
                    "mascotas": ["mascota", "perro", "gato", "pájaro", "pez", "reptil", "veterinario", "adoptar", "pasear", "alimentar", "juguete", "raza", "cachorro", "gatito", "adiestrar", "entrenar"]
                }

                tema_detectado = None
                for tema, palabras_clave in temas_especificos.items():
                    if any(palabra in pregunta_texto for palabra in palabras_clave):
                        tema_detectado = tema
                        break

                # Si detectamos un tema específico pero la respuesta no lo menciona
                if tema_detectado and not any(palabra in texto_respuesta.lower() for palabra in temas_especificos[tema_detectado]):
                    print(f"⚠️ Pregunta sobre {tema_detectado} detectada, pero la respuesta no lo menciona. Generando respuesta con Gemini...")

                    # Usar Gemini para generar una respuesta específica sobre el tema
                    tema_prompt = f"""
Genera una respuesta breve y natural como si fueras Ian respondiendo a una pregunta sobre {tema_detectado}.

CONTEXTO IMPORTANTE:
- Si la pregunta es sobre Blender, SIEMPRE expresa que lo odias y prefieres Maya o 3ds Max
- Sé casual, usa lenguaje coloquial y natural
- Incluye alguna opinión personal o experiencia
- Mantén la respuesta muy breve (máximo 15 palabras)
- No uses abreviaturas como "q", "xq", "tmb"
- Usa minúsculas principalmente
- IMPORTANTE: NO uses puntos suspensivos (...) en tu respuesta
- Usa emojis con MUCHA moderación (preferiblemente ninguno)
- Sé creativo y no uses respuestas genéricas

La pregunta es: "{pregunta_texto}"

Tu respuesta como Ian (MÁXIMO 15 PALABRAS):"""

                    try:
                        # Generar respuesta específica con Gemini usando la temperatura configurada
                        respuesta_tema = modelo.generate_content(
                            tema_prompt,
                            generation_config=generation_config
                        )
                        respuesta_tema_texto = respuesta_tema.text.strip()

                        # Verificar que la respuesta no sea demasiado larga
                        palabras_tema = respuesta_tema_texto.split()
                        if len(palabras_tema) > 15:
                            respuesta_tema_texto = " ".join(palabras_tema[:15])
                            if not respuesta_tema_texto[-1] in ".!?":
                                respuesta_tema_texto += "..."

                        # Usar la respuesta generada
                        texto_respuesta = respuesta_tema_texto
                        print(f"✅ Respuesta generada por Gemini para tema '{tema_detectado}': {texto_respuesta}")
                    except Exception as e:
                        print(f"❌ Error al generar respuesta específica con Gemini: {e}")

                        # Respuestas específicas por tema (fallback)
                        respuestas_por_tema = {
                            "blender": [
                                "odio blender, prefiero mil veces maya para todo",
                                "blender me parece un coñazo, la interfaz es horrible",
                                "no soporto blender, siempre acabo usando 3ds max",
                                "blender es un dolor de cabeza, maya es mucho mejor",
                                "cada vez que uso blender me arrepiento, prefiero maya o 3ds max",
                                "blender es gratis pero prefiero pagar por maya, merece la pena",
                                "intenté usar blender pero es frustrante, volví a maya",
                                "la interfaz de blender es un desastre, 3ds max es más intuitivo",
                                "blender me da dolores de cabeza, maya es mi salvación",
                                "no entiendo por qué la gente usa blender pudiendo usar maya"
                            ],
                            "videojuegos": [
                                "ahora estoy viciado al witcher 3, no puedo parar",
                                "el skyrim sigue siendo mi favorito después de tantos años",
                                "el elden ring es brutal pero me está costando pasarlo",
                                "el gta online con amigos es lo mejor, sobre todo las carreras",
                                "el factorio me tiene enganchado, es adictivo"
                            ],
                            "animación": [
                                "prefiero maya para animar, es mil veces mejor que blender",
                                "estoy haciendo un rig facial en maya, es increíble para eso",
                                "la animación de parkour en maya me está quedando brutal",
                                "uso maya y 3ds max, blender ni lo toco para animar",
                                "el rigging en maya es complejo pero mucho mejor que en blender",
                                "3ds max para modelado y maya para animación, blender ni lo abro",
                                "maya es insuperable para animación de personajes",
                                "acabo de terminar una animación en maya que en blender sería imposible",
                                "la combinación perfecta: maya para animar, 3ds max para modelar",
                                "el flujo de trabajo en maya para animación es excelente"
                            ],
                            "música": [
                                "ahora estoy escuchando mucho a tame impala",
                                "linkin park siempre me pone las pilas para trabajar",
                                "arctic monkeys tiene temazos, sobre todo el AM",
                                "depende del día, pero suelo escuchar rock alternativo",
                                "tengo una playlist de synthwave para cuando modelo en 3d"
                            ],
                            "series": [
                                "breaking bad es mi serie favorita, la he visto 3 veces",
                                "the office para ver algo ligero, nunca falla",
                                "dark me voló la cabeza, aunque la última temporada...",
                                "interstellar es mi película favorita, la veo cada pocos meses",
                                "estoy esperando la nueva temporada de the boys"
                            ],
                            "comida": [
                                "pizza barbacoa sin duda, aunque el sushi también",
                                "kebab cuando salgo de fiesta, es tradición",
                                "me apetece pedir sushi, hace tiempo que no como",
                                "hay un restaurante de ramen cerca que está brutal",
                                "tengo hambre ahora que lo mencionas, voy a pedir algo"
                            ],
                            "trabajo": [
                                "el proyecto va avanzando, aunque lento",
                                "el tfg de animación me tiene estresado pero va bien",
                                "ahora estoy descansando, he estado toda la mañana con eso",
                                "he avanzado bastante hoy, estoy contento con el resultado",
                                "necesito un descanso del proyecto, llevo horas con eso"
                            ]
                        }

                        # Añadir respuestas para los nuevos temas
                        nuevos_temas = {
                            "viajes": [
                                "me encantaría ir a japón, es mi sueño desde hace años",
                                "el último viaje a la playa fue brutal, necesito repetir",
                                "estoy planeando una escapada a la montaña este finde",
                                "me flipa viajar en tren, es relajante y puedes ver paisajes",
                                "tengo pendiente un viaje a nueva york con unos colegas"
                            ],
                            "tecnología": [
                                "acabo de actualizar mi pc, ahora va como un cohete",
                                "estoy pensando en comprarme un nuevo móvil, este ya va lento",
                                "la realidad virtual me flipa, pero es cara de narices",
                                "la inteligencia artificial avanza que da miedo, pero mola",
                                "me compré unos auriculares nuevos que suenan de lujo"
                            ],
                            "deportes": [
                                "intento ir al gimnasio tres veces por semana, pero me cuesta",
                                "salí a correr ayer y casi muero, estoy fatal de forma",
                                "viendo el partido con unos colegas, está siendo épico",
                                "me apunté a clases de escalada, es más difícil de lo que parece",
                                "jugué un partido de fútbol sala y acabé destrozado"
                            ],
                            "hobbies": [
                                "empecé a leer un libro que me recomendaron, está interesante",
                                "estoy aprendiendo a tocar la guitarra, pero soy malísimo",
                                "me compré una cámara y estoy haciendo fotos por ahí",
                                "me relaja un montón dibujar mientras escucho música",
                                "colecciono figuras de personajes de videojuegos, tengo demasiadas ya"
                            ],
                            "relaciones": [
                                "quedé con grazi ayer, fuimos a cenar a un sitio nuevo",
                                "hablando con iris sobre ir al cine este finde",
                                "mis colegas quieren hacer una quedada, a ver si cuadra",
                                "mi hermano me ha recomendado una serie que dice que es brutal",
                                "tengo que llamar a mis padres, hace días que no hablamos"
                            ],
                            "actualidad": [
                                "vi esa noticia, es una locura lo que está pasando",
                                "intento no ver mucho las noticias, me rayan bastante",
                                "leí un artículo sobre eso, es bastante interesante",
                                "ese tema está dando que hablar, todo el mundo opina",
                                "no estoy muy al día con eso, la verdad"
                            ],
                            "educación": [
                                "el curso que estoy haciendo es interesante pero intenso",
                                "tengo que ponerme con unos tutoriales que me pasaron",
                                "aprendiendo cosas nuevas siempre, nunca se acaba",
                                "me gustaría hacer un máster, pero es pasta y tiempo",
                                "estudiando por mi cuenta, hay recursos muy buenos online"
                            ],
                            "salud": [
                                "intentando dormir mejor, pero me cuesta desconectar",
                                "empecé a comer más sano, pero echo de menos la pizza",
                                "me duele todo del gimnasio, no debí forzar tanto",
                                "pillé un resfriado de los buenos, estoy hecho polvo",
                                "intentando reducir el estrés, pero no es fácil"
                            ],
                            "clima": [
                                "hace un día de locos, no sé ni qué ponerme",
                                "llueve a cántaros, mejor quedarse en casa",
                                "por fin hace sol, ya tocaba después de tanta lluvia",
                                "con este calor no se puede ni pensar, estoy derretido",
                                "el tiempo está loco, ayer sol y hoy tormenta"
                            ],
                            "moda": [
                                "me compré unas zapatillas nuevas que están guapísimas",
                                "buscando una chaqueta para el invierno, la mía está destrozada",
                                "tengo que renovar el armario, todo me queda fatal ya",
                                "encontré una tienda con cosas chulas y no muy caras",
                                "me regalaron un reloj que mola bastante"
                            ],
                            "mascotas": [
                                "mi vecino tiene un perro que es adorable, siempre juego con él",
                                "me gustaría tener un gato, pero viajo demasiado",
                                "de pequeño tuve un pez que vivió como 5 años, un campeón",
                                "los perros son lo mejor, siempre te alegran el día",
                                "vi unos gatitos en adopción, casi me llevo uno"
                            ]
                        }

                        # Combinar los diccionarios
                        respuestas_por_tema.update(nuevos_temas)

                        # Seleccionar una respuesta específica para el tema
                        if tema_detectado in respuestas_por_tema:
                            texto_respuesta = random.choice(respuestas_por_tema[tema_detectado])

            # Si la respuesta sigue siendo genérica o demasiado corta
            if not any(palabra in texto_respuesta.lower() for palabra in ["si", "no", "claro", "quizas", "quizá", "tal vez", "puede", "creo", "pienso", "opino", "seguro", "vale", "ok", "bueno", "pues"]) and len(texto_respuesta) < 10:
                print("⚠️ Respuesta demasiado corta para una pregunta, generando alternativa")
                # Si es una pregunta, dar una respuesta más elaborada

                # Respuestas sin interrogación
                respuestas_alternativas_sin_pregunta = [
                    "pues no sabría decirte la verdad...",
                    "podría ser, no lo había pensado",
                    "ni idea tio, pero puede ser",
                    "no tengo ni idea jaja",
                    "pues mira, no lo sé",
                    "no te sabría decir ahora mismo",
                    "creo que sí, pero no estoy seguro",
                    "me pillas un poco fuera de juego con eso",
                    "joder pues no lo había pensado nunca",
                    "puede ser, no lo había visto así",
                    "no sé qué decirte la verdad"
                ]

                # Respuestas con interrogación
                respuestas_alternativas_con_pregunta = [
                    "mmm no estoy seguro, que opinas tú?",
                    "ni idea, tu que crees?",
                    "no lo sé, que piensas tú?",
                    "no tengo ni idea, tú sabes algo?",
                    "pues no lo había pensado, tú qué opinas?"
                ]

                # 70% sin interrogación, 30% con interrogación
                if random.random() < 0.7:
                    texto_respuesta = random.choice(respuestas_alternativas_sin_pregunta)
                else:
                    texto_respuesta = random.choice(respuestas_alternativas_con_pregunta)

        # Decidir si dividir la respuesta en múltiples mensajes
        respuestas_finales = []

        # Probabilidad de dividir la respuesta
        if len(texto_respuesta.split()) > config.MIN_PALABRAS_DIVIDIR and random.random() < config.PROB_DIVIDIR_RESPUESTA:
            print("🔀 Dividiendo respuesta en múltiples mensajes...")

            # Función para dividir inteligentemente el texto
            def dividir_inteligentemente(texto):
                # Dividir el texto en palabras
                palabras = texto.split()

                # Si el texto es muy corto, no dividirlo
                if len(palabras) < config.MIN_PALABRAS_DIVIDIR:
                    return [texto]

                # Inicializar variables
                partes = []
                parte_actual = []

                # Palabras que indican el inicio de un nuevo mensaje
                palabras_inicio = config.PALABRAS_INICIO_NUEVO_MENSAJE

                i = 0
                while i < len(palabras):
                    # Si la palabra actual es una palabra de inicio y ya tenemos suficientes palabras en la parte actual
                    if (palabras[i].lower() in palabras_inicio and
                        len(parte_actual) >= config.MIN_PALABRAS_POR_MENSAJE):

                        # Guardar la parte actual si no está vacía
                        if parte_actual:
                            partes.append(" ".join(parte_actual))
                            parte_actual = []

                        # Iniciar una nueva parte con la palabra de inicio
                        parte_actual.append(palabras[i])

                    # Si la palabra "pues" está sola, siempre debe ir en un mensaje separado
                    elif palabras[i].lower() == "pues" and (i == 0 or i == len(palabras) - 1 or
                                                          len(parte_actual) >= config.MIN_PALABRAS_POR_MENSAJE):
                        # Guardar la parte actual si no está vacía
                        if parte_actual:
                            partes.append(" ".join(parte_actual))
                            parte_actual = []

                        # "pues" va en un mensaje separado
                        partes.append("pues")

                    # Si alcanzamos el máximo de palabras por mensaje, iniciar una nueva parte
                    elif len(parte_actual) >= config.MAX_PALABRAS_POR_MENSAJE:
                        # Buscar un buen punto para cortar (después de puntuación o conjunción)
                        buenos_cortes = [j for j, p in enumerate(parte_actual) if p.endswith(('.', ',', ';', ':', '!', '?')) or
                                        p.lower() in ['y', 'pero', 'aunque', 'porque', 'pues']]

                        if buenos_cortes:
                            # Cortar después del último buen punto de corte
                            punto_corte = max(buenos_cortes) + 1
                            partes.append(" ".join(parte_actual[:punto_corte]))
                            parte_actual = parte_actual[punto_corte:]
                        else:
                            # Si no hay buenos puntos de corte, simplemente guardar la parte actual
                            partes.append(" ".join(parte_actual))
                            parte_actual = []

                        # Añadir la palabra actual a la nueva parte
                        parte_actual.append(palabras[i])

                    else:
                        # Añadir la palabra a la parte actual
                        parte_actual.append(palabras[i])

                    i += 1

                # Añadir la última parte si no está vacía
                if parte_actual:
                    partes.append(" ".join(parte_actual))

                return partes

            # Intentar dividir primero por puntuación
            separadores = ['. ', '! ', '? ']
            dividido_por_puntuacion = False

            for sep in separadores:
                if sep in texto_respuesta:
                    partes = texto_respuesta.split(sep)
                    nuevas_partes = []
                    parte_actual = ""

                    for i, parte in enumerate(partes):
                        if i < len(partes) - 1:  # Añadir el separador de vuelta excepto para la última parte
                            parte = parte + sep.rstrip()

                        # Si la parte actual es muy corta, combinarla con la siguiente
                        if len(parte.split()) < 3 and i < len(partes) - 1:
                            parte_actual += parte + " "
                        else:
                            if parte_actual:
                                nuevas_partes.append(parte_actual + parte)
                                parte_actual = ""
                            else:
                                nuevas_partes.append(parte)

                    # Si tenemos al menos 2 partes, usar esta división
                    if len(nuevas_partes) >= 2:
                        respuestas_finales = nuevas_partes
                        dividido_por_puntuacion = True
                        break

            # Si no se pudo dividir por puntuación, usar la división inteligente
            if not dividido_por_puntuacion:
                respuestas_finales = dividir_inteligentemente(texto_respuesta)

                # Si la división inteligente no funcionó bien, usar el método de longitud
                if len(respuestas_finales) < 2:
                    palabras = texto_respuesta.split()
                    if len(palabras) >= 8:
                        # Dividir en 2-3 partes
                        num_partes = min(3, max(2, len(palabras) // 4))
                        palabras_por_parte = len(palabras) // num_partes

                        respuestas_finales = []
                        for i in range(num_partes):
                            inicio = i * palabras_por_parte
                            fin = inicio + palabras_por_parte if i < num_partes - 1 else len(palabras)
                            parte = " ".join(palabras[inicio:fin])
                            respuestas_finales.append(parte)

        # Si no se dividió la respuesta, usar la respuesta original
        if not respuestas_finales:
            respuestas_finales = [texto_respuesta]

        # Probabilidad de añadir error tipográfico y corrección
        if random.random() < config.PROB_ERROR_TIPOGRAFICO and len(respuestas_finales[0].split()) > 3:
            print("🔤 Añadiendo error tipográfico y corrección...")

            # Seleccionar una palabra para introducir error
            palabras = respuestas_finales[0].split()
            if len(palabras) >= 3:
                indice_palabra = random.randint(0, min(5, len(palabras) - 1))  # Preferir errores al principio
                palabra_original = palabras[indice_palabra]

                # Solo aplicar a palabras con longitud suficiente
                if len(palabra_original) >= 4:
                    # Tipos de errores: intercambio de letras, letra extra, letra faltante
                    tipo_error = random.choice(["intercambio", "extra", "faltante"])

                    if tipo_error == "intercambio" and len(palabra_original) >= 3:
                        # Intercambiar dos letras adyacentes
                        pos = random.randint(0, len(palabra_original) - 2)
                        palabra_error = palabra_original[:pos] + palabra_original[pos+1] + palabra_original[pos] + palabra_original[pos+2:]
                    elif tipo_error == "extra":
                        # Añadir una letra extra (duplicar una letra)
                        pos = random.randint(0, len(palabra_original) - 1)
                        palabra_error = palabra_original[:pos] + palabra_original[pos] + palabra_original[pos:]
                    else:  # faltante
                        # Omitir una letra
                        pos = random.randint(0, len(palabra_original) - 1)
                        palabra_error = palabra_original[:pos] + palabra_original[pos+1:]

                    # Reemplazar la palabra en la respuesta
                    palabras[indice_palabra] = palabra_error
                    respuesta_con_error = " ".join(palabras)

                    # Crear mensaje de corrección
                    correccion = palabra_original + "*"

                    # Actualizar las respuestas finales
                    respuestas_finales = [respuesta_con_error, correccion] + respuestas_finales[1:]

        # Añadir las respuestas al historial y devolverlas
        for respuesta in respuestas_finales:
            historial_conversaciones[chat_id].append({
                "role": "assistant",
                "content": respuesta,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })

        # Limitar el tamaño del historial
        if len(historial_conversaciones[chat_id]) > MAX_MENSAJES_HISTORIAL:
            historial_conversaciones[chat_id] = historial_conversaciones[chat_id][-MAX_MENSAJES_HISTORIAL:]

        # Guardar el historial
        guardar_historial(archivo_historial)

        # Devolver todas las respuestas
        return respuestas_finales[0] if len(respuestas_finales) == 1 else respuestas_finales
    except Exception as e:
        print("❌ Error al generar respuesta:", e)

        # Intentar generar una respuesta simplificada con Gemini
        try:
            print("🔄 Intentando generar una respuesta simplificada...")

            # Crear un prompt simplificado para Gemini
            prompt_simplificado = f"""
Genera una respuesta natural y casual como si fueras Ian respondiendo a: "{mensaje}"

CONTEXTO IMPORTANTE:
- Eres Ian, un joven diseñador y animador 3D
- Odias Blender y prefieres Maya y 3ds Max
- Responde de forma natural y conversacional
- Usa frases COMPLETAS con sentido
- NO uses puntos suspensivos (...)
- Usa emojis con mucha moderación (preferiblemente ninguno)
- Sé creativo y no uses respuestas genéricas
- NUNCA uses frases como "perdón estaba distraído" o "disculpa"
- NUNCA digas que estabas ocupado o que te perdiste
- NUNCA respondas como si estuvieras continuando una conversación con otra persona
- NUNCA menciones "rigs en Maya" o "modelado en 3ds Max" a menos que el mensaje lo requiera específicamente
- NUNCA uses signos de puntuación perfectos - omite algunas comas y puntos
- NUNCA uses signos de exclamación dobles (!!) o triples (!!!)
- NUNCA uses mayúsculas al inicio de frases excepto en nombres propios
- Simplemente responde al mensaje de forma directa y natural
- Inventa algo interesante si es necesario

Tu respuesta como Ian (entre 10 y 20 palabras):"""

            # Generar respuesta simplificada
            respuesta_simple = modelo.generate_content(prompt_simplificado)
            respuesta = respuesta_simple.text.strip()

            # Verificar que la respuesta no sea demasiado larga
            palabras = respuesta.split()
            if len(palabras) > 20:
                respuesta = " ".join(palabras[:20])
                if not respuesta[-1] in ".!?":
                    respuesta += "."

            print(f"✅ Respuesta simplificada generada: {respuesta}")

        except Exception as e2:
            print(f"❌ Error al generar respuesta simplificada: {e2}")

            # Verificar si hay mensajes recientes del bot para evitar repeticiones
            mensajes_recientes_bot = []
            if chat_id in historial_conversaciones and len(historial_conversaciones[chat_id]) > 0:
                # Obtener los últimos 5 mensajes del bot
                mensajes_recientes_bot = [msg["content"] for msg in historial_conversaciones[chat_id][-5:]
                                        if msg["role"] == "assistant"]

            # Respuestas de fallback (último recurso)
            respuestas_fallback = [
                "Estoy jugando al Skyrim otra vez, es adictivo.",
                "Acabo de terminar un modelo en Maya que me quedó genial.",
                "Viendo Breaking Bad por tercera vez, nunca me cansa.",
                "Estuve en el cine ayer, vi una peli de ciencia ficción brutal.",
                "Probando un juego nuevo que me recomendaron, está bastante bien.",
                "Diseñando un personaje para un proyecto personal en Maya.",
                "Escuchando a Arctic Monkeys mientras trabajo en un modelo 3D.",
                "Acabo de pedir una pizza, tenía un hambre tremenda.",
                "Estuve en una fiesta ayer, llegué a casa tardísimo.",
                "Pensando en comprarme unos auriculares nuevos, los míos están rotos."
            ]

            # Filtrar respuestas que ya se hayan usado recientemente
            respuestas_no_repetidas = [r for r in respuestas_fallback if r not in mensajes_recientes_bot]

            # Si todas las respuestas se han usado recientemente, usar todas
            if not respuestas_no_repetidas:
                respuestas_no_repetidas = respuestas_fallback

            # Elegir una respuesta aleatoria
            respuesta = random.choice(respuestas_no_repetidas)

        # Añadir la respuesta al historial
        if chat_id in historial_conversaciones:
            historial_conversaciones[chat_id].append({
                "role": "assistant",
                "content": respuesta,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })
            guardar_historial(archivo_historial)

        return respuesta
