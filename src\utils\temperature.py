"""
Módulo para gestionar la temperatura de IA del bot de WhatsApp.
Proporciona funciones para ajustar parámetros de personalidad basados en la temperatura.
"""

import random
from src.config import (
    TEMPERATURA_DEFAULT, 
    TEMPERATURA_MIN, 
    TEMPERATURA_MAX,
    PROB_DIVIDIR_RESPUESTA,
    PROB_ERROR_TIPOGRAFICO
)

# Variable global para almacenar la temperatura actual
temperatura_actual = TEMPERATURA_DEFAULT

def establecer_temperatura(valor):
    """
    Establece la temperatura de IA del bot.
    
    Args:
        valor (float): Valor de temperatura entre 0.0 y 1.0
        
    Returns:
        float: El valor de temperatura establecido
    """
    global temperatura_actual
    
    # Asegurar que el valor esté dentro del rango permitido
    valor = max(TEMPERATURA_MIN, min(TEMPERATURA_MAX, valor))
    temperatura_actual = valor
    
    return temperatura_actual

def obtener_temperatura():
    """
    Obtiene la temperatura actual de IA del bot.
    
    Returns:
        float: El valor de temperatura actual
    """
    return temperatura_actual

def ajustar_probabilidad(prob_base, factor_ajuste=1.0):
    """
    Ajusta una probabilidad base según la temperatura actual.
    
    Args:
        prob_base (float): Probabilidad base a ajustar
        factor_ajuste (float): Factor adicional de ajuste (por defecto 1.0)
        
    Returns:
        float: Probabilidad ajustada según la temperatura
    """
    # Calcular el factor de temperatura (0.5 es neutral)
    # Temperaturas más altas aumentan la probabilidad, más bajas la disminuyen
    factor_temp = 1.0 + (temperatura_actual - 0.5) * 2.0 * factor_ajuste
    
    # Ajustar la probabilidad base
    prob_ajustada = prob_base * factor_temp
    
    # Asegurar que la probabilidad esté entre 0.0 y 1.0
    return max(0.0, min(1.0, prob_ajustada))

def calcular_temperatura_gemini():
    """
    Calcula el valor de temperatura para la API de Gemini basado en la temperatura del bot.
    
    La temperatura de Gemini suele estar entre 0.0 y 1.0, donde:
    - 0.0 = Respuestas deterministas y conservadoras
    - 1.0 = Respuestas más creativas y diversas
    
    Returns:
        float: Valor de temperatura para Gemini API
    """
    # Mapear nuestra temperatura a la escala de Gemini
    # Nuestra temperatura alta (1.0) = Gemini temperatura alta (0.9-1.0)
    # Nuestra temperatura baja (0.0) = Gemini temperatura baja (0.1-0.2)
    
    # Usamos una escala no lineal para dar más control en los extremos
    if temperatura_actual <= 0.5:
        # Mapear 0.0-0.5 a 0.1-0.7 (más conservador)
        return 0.1 + (temperatura_actual * 1.2)
    else:
        # Mapear 0.5-1.0 a 0.7-1.0 (más creativo)
        return 0.7 + ((temperatura_actual - 0.5) * 0.6)

def obtener_parametros_ajustados():
    """
    Obtiene un diccionario con todos los parámetros ajustados según la temperatura actual.
    
    Returns:
        dict: Diccionario con parámetros ajustados
    """
    # Probabilidad de dividir respuestas en múltiples mensajes
    # Temperatura alta = más división de mensajes
    prob_dividir = ajustar_probabilidad(PROB_DIVIDIR_RESPUESTA, 0.8)
    
    # Probabilidad de añadir errores tipográficos
    # Temperatura alta = más errores tipográficos
    prob_error = ajustar_probabilidad(PROB_ERROR_TIPOGRAFICO, 1.2)
    
    # Probabilidad de usar emojis
    # Temperatura alta = más uso de emojis
    prob_emoji = ajustar_probabilidad(0.3, 1.5)
    
    # Probabilidad de usar interrogación
    # Temperatura alta = más preguntas
    prob_interrogacion = ajustar_probabilidad(0.3, 0.7)
    
    # Temperatura para Gemini API
    temp_gemini = calcular_temperatura_gemini()
    
    # Probabilidad de improvisar historias
    # Temperatura alta = más improvisación
    prob_improvisar = ajustar_probabilidad(0.4, 1.8)
    
    return {
        "prob_dividir_respuesta": prob_dividir,
        "prob_error_tipografico": prob_error,
        "prob_emoji": prob_emoji,
        "prob_interrogacion": prob_interrogacion,
        "temperatura_gemini": temp_gemini,
        "prob_improvisar": prob_improvisar
    }

def debe_usar_emoji():
    """
    Determina si se debe usar un emoji basado en la temperatura actual.
    
    Returns:
        bool: True si se debe usar emoji, False en caso contrario
    """
    params = obtener_parametros_ajustados()
    return random.random() < params["prob_emoji"]

def debe_usar_interrogacion():
    """
    Determina si se debe usar interrogación basado en la temperatura actual.
    
    Returns:
        bool: True si se debe usar interrogación, False en caso contrario
    """
    params = obtener_parametros_ajustados()
    return random.random() < params["prob_interrogacion"]

def debe_improvisar():
    """
    Determina si el bot debe improvisar una historia o respuesta creativa.
    
    Returns:
        bool: True si debe improvisar, False en caso contrario
    """
    params = obtener_parametros_ajustados()
    return random.random() < params["prob_improvisar"]
