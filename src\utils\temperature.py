"""
Módulo para gestionar la temperatura de IA del bot de WhatsApp.
Proporciona funciones para ajustar parámetros de personalidad basados en la temperatura.
"""

import random
from src.config.config import (
    TEMPERATURA_DEFAULT,
    TEMPERATURA_MIN,
    TEMPERATURA_MAX,
    PROB_DIVIDIR_RESPUESTA,
    PROB_ERROR_TIPOGRAFICO
)

# Variable global para almacenar la temperatura actual
temperatura_actual = TEMPERATURA_DEFAULT

def establecer_temperatura(valor):
    """
    Establece la temperatura de IA del bot.
    
    Args:
        valor (float): Valor de temperatura entre 0.0 y 1.0
        
    Returns:
        float: El valor de temperatura establecido
    """
    global temperatura_actual
    
    # Asegurar que el valor esté dentro del rango permitido
    valor = max(TEMPERATURA_MIN, min(TEMPERATURA_MAX, valor))
    temperatura_actual = valor
    
    return temperatura_actual

def obtener_temperatura():
    """
    Obtiene la temperatura actual de IA del bot.
    
    Returns:
        float: El valor de temperatura actual
    """
    return temperatura_actual

def ajustar_probabilidad(prob_base, factor_ajuste=1.0):
    """
    Ajusta una probabilidad base según la temperatura actual.
    
    Args:
        prob_base (float): Probabilidad base a ajustar
        factor_ajuste (float): Factor adicional de ajuste (por defecto 1.0)
        
    Returns:
        float: Probabilidad ajustada según la temperatura
    """
    # Calcular el factor de temperatura (0.5 es neutral)
    # Temperaturas más altas aumentan la probabilidad, más bajas la disminuyen
    factor_temp = 1.0 + (temperatura_actual - 0.5) * 2.0 * factor_ajuste
    
    # Ajustar la probabilidad base
    prob_ajustada = prob_base * factor_temp
    
    # Asegurar que la probabilidad esté entre 0.0 y 1.0
    return max(0.0, min(1.0, prob_ajustada))

def calcular_temperatura_gemini():
    """
    Calcula el valor de temperatura para la API de Gemini basado en la temperatura del bot.

    La temperatura de Gemini suele estar entre 0.0 y 1.0, donde:
    - 0.0 = Respuestas deterministas y conservadoras
    - 1.0 = Respuestas más creativas y diversas

    Returns:
        float: Valor de temperatura para Gemini API
    """
    # Mapear nuestra temperatura a la escala de Gemini de forma más agresiva
    # para que el bot sea más flexible y menos restrictivo

    if temperatura_actual <= 0.3:
        # Mapear 0.0-0.3 a 0.2-0.6 (menos conservador que antes)
        return 0.2 + (temperatura_actual * 1.33)
    elif temperatura_actual <= 0.7:
        # Mapear 0.3-0.7 a 0.6-0.9 (rango medio más amplio)
        return 0.6 + ((temperatura_actual - 0.3) * 0.75)
    else:
        # Mapear 0.7-1.0 a 0.9-1.0 (máxima creatividad y flexibilidad)
        return 0.9 + ((temperatura_actual - 0.7) * 0.33)

def obtener_parametros_ajustados():
    """
    Obtiene un diccionario con todos los parámetros ajustados según la temperatura actual.
    
    Returns:
        dict: Diccionario con parámetros ajustados
    """
    # Probabilidad de dividir respuestas en múltiples mensajes
    # Temperatura alta = más división de mensajes
    prob_dividir = ajustar_probabilidad(PROB_DIVIDIR_RESPUESTA, 0.8)
    
    # Probabilidad de añadir errores tipográficos
    # Temperatura alta = más errores tipográficos
    prob_error = ajustar_probabilidad(PROB_ERROR_TIPOGRAFICO, 1.2)
    
    # Probabilidad de usar emojis
    # Temperatura alta = más uso de emojis
    prob_emoji = ajustar_probabilidad(0.3, 1.5)
    
    # Probabilidad de usar interrogación
    # Temperatura alta = más preguntas
    prob_interrogacion = ajustar_probabilidad(0.3, 0.7)
    
    # Temperatura para Gemini API
    temp_gemini = calcular_temperatura_gemini()
    
    # Probabilidad de improvisar historias
    # Temperatura alta = más improvisación
    prob_improvisar = ajustar_probabilidad(0.4, 1.8)
    
    return {
        "prob_dividir_respuesta": prob_dividir,
        "prob_error_tipografico": prob_error,
        "prob_emoji": prob_emoji,
        "prob_interrogacion": prob_interrogacion,
        "temperatura_gemini": temp_gemini,
        "prob_improvisar": prob_improvisar
    }

def debe_usar_emoji():
    """
    Determina si se debe usar un emoji basado en la temperatura actual.
    
    Returns:
        bool: True si se debe usar emoji, False en caso contrario
    """
    params = obtener_parametros_ajustados()
    return random.random() < params["prob_emoji"]

def debe_usar_interrogacion():
    """
    Determina si se debe usar interrogación basado en la temperatura actual.
    
    Returns:
        bool: True si se debe usar interrogación, False en caso contrario
    """
    params = obtener_parametros_ajustados()
    return random.random() < params["prob_interrogacion"]

def debe_improvisar():
    """
    Determina si el bot debe improvisar una historia o respuesta creativa.

    Returns:
        bool: True si debe improvisar, False en caso contrario
    """
    params = obtener_parametros_ajustados()
    return random.random() < params["prob_improvisar"]

def obtener_factor_permisividad():
    """
    Calcula un factor de permisividad basado en la temperatura actual.
    A mayor temperatura, más permisivo y menos restrictivo será el bot.

    Returns:
        float: Factor de permisividad (0.0 = muy restrictivo, 1.0 = muy permisivo)
    """
    # La permisividad aumenta exponencialmente con la temperatura
    # para que temperaturas altas hagan al bot mucho más flexible
    return min(1.0, temperatura_actual ** 0.7)

def debe_aceptar_peticion():
    """
    Determina si el bot debe aceptar una petición del usuario.
    A mayor temperatura, más probable que acepte peticiones.

    Returns:
        bool: True si debe aceptar la petición, False en caso contrario
    """
    factor_permisividad = obtener_factor_permisividad()
    # Base de aceptación del 60% que aumenta con la temperatura
    prob_aceptar = 0.6 + (factor_permisividad * 0.35)
    return random.random() < prob_aceptar

def debe_ser_creativo():
    """
    Determina si el bot debe ser más creativo en sus respuestas.

    Returns:
        bool: True si debe ser creativo, False en caso contrario
    """
    # La creatividad aumenta linealmente con la temperatura
    prob_creatividad = 0.3 + (temperatura_actual * 0.6)
    return random.random() < prob_creatividad

def obtener_nivel_restriccion():
    """
    Obtiene el nivel de restricción del bot basado en la temperatura.

    Returns:
        str: Nivel de restricción ('bajo', 'medio', 'alto')
    """
    if temperatura_actual >= 0.7:
        return 'bajo'  # Muy permisivo, acepta casi todo
    elif temperatura_actual >= 0.4:
        return 'medio'  # Moderadamente permisivo
    else:
        return 'alto'  # Más restrictivo pero no excesivamente

def detectar_peticion_directa(mensaje):
    """
    Detecta si el mensaje contiene una petición directa que debe ser cumplida inmediatamente.

    Args:
        mensaje (str): El mensaje del usuario

    Returns:
        dict: Información sobre la petición detectada o None si no hay petición
    """
    mensaje_lower = mensaje.lower()

    # Patrones de peticiones directas
    patrones_rap = [
        "haz un rap", "hazme un rap", "escribe un rap", "escríbeme un rap",
        "rap de", "rap sobre", "rap del", "rap para"
    ]

    patrones_historia = [
        "cuenta una historia", "cuéntame una historia", "inventa una historia",
        "haz una historia", "escribe una historia"
    ]

    patrones_poema = [
        "haz un poema", "hazme un poema", "escribe un poema", "escríbeme un poema"
    ]

    patrones_cancion = [
        "haz una canción", "hazme una canción", "escribe una canción", "canta"
    ]

    # Detectar tipo de petición
    if any(patron in mensaje_lower for patron in patrones_rap):
        # Extraer el tema del rap
        tema = "general"
        if "kebab" in mensaje_lower:
            tema = "kebab"
        elif "maya" in mensaje_lower or "3ds max" in mensaje_lower:
            tema = "software 3d"
        elif "grazi" in mensaje_lower:
            tema = "grazi"

        return {
            "tipo": "rap",
            "tema": tema,
            "debe_cumplir": True
        }

    elif any(patron in mensaje_lower for patron in patrones_historia):
        return {
            "tipo": "historia",
            "tema": "general",
            "debe_cumplir": True
        }

    elif any(patron in mensaje_lower for patron in patrones_poema):
        return {
            "tipo": "poema",
            "tema": "general",
            "debe_cumplir": True
        }

    elif any(patron in mensaje_lower for patron in patrones_cancion):
        return {
            "tipo": "cancion",
            "tema": "general",
            "debe_cumplir": True
        }

    return None

def generar_rap_kebab():
    """
    Genera un rap específico sobre kebabs.

    Returns:
        str: El rap generado
    """
    raps_kebab = [
        """yo, escucha esto tio
kebab en la mesa, durum en la mano
salsa picante, extra de carne hermano
por la noche voy al turco del barrio
kebab con patatas, menudo escenario

cebolla, tomate, todo bien mezclado
kebab de pollo, nunca me ha fallado
grazi dice que como demasiado
pero el kebab tio, nunca está de más""",

        """kebab kebab, mi comida favorita
durum gigante, menuda comidita
salsa blanca, salsa picante
kebab de ternera, siempre elegante

por el raval busco el mejor sitio
kebab con queso, eso es mi vicio
tres de la mañana, hambre canina
kebab de pollo con una cocacola fina""",

        """esto va por todos los kebabs del mundo
durum, lahmacun, sabor profundo
carne al spit, girando sin parar
kebab perfecto, me voy a hartar

salsa de yogur, ajo y perejil
kebab casero, sabor muy sutil
pero el del turco siempre está mejor
kebab con todo, eres mi amor"""
    ]

    import random
    return random.choice(raps_kebab)
