"""
Test para el generador de historias improvisadas.
Este script prueba la funcionalidad del módulo story_generator.py.
"""

import sys
import os
import unittest
import random

# Añadir el directorio raíz al path para poder importar los módulos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Importar el módulo de generación de historias
from src.utils.story_generator import (
    generar_historia_improvisada,
    generar_excusa_para_no_contar,
    decidir_contar_historia
)

# Importar el módulo de información personal
from src.utils.personal_info import cargar_info_personal

class TestStoryGenerator(unittest.TestCase):
    """Clase para probar el generador de historias."""

    def setUp(self):
        """Configuración inicial para las pruebas."""
        # Fijar la semilla para reproducibilidad
        random.seed(42)
        # Cargar información personal para las pruebas
        self.info_personal = cargar_info_personal()

    def test_generar_historia_improvisada(self):
        """Prueba la generación de historias improvisadas."""
        # Generar una historia sin información personal
        historia_sin_info = generar_historia_improvisada()
        self.assertIsNotNone(historia_sin_info)
        self.assertIsInstance(historia_sin_info, str)
        self.assertTrue(len(historia_sin_info) > 0)
        
        # Generar una historia con información personal
        historia_con_info = generar_historia_improvisada(self.info_personal)
        self.assertIsNotNone(historia_con_info)
        self.assertIsInstance(historia_con_info, str)
        self.assertTrue(len(historia_con_info) > 0)
        
        # Verificar que las historias son diferentes
        self.assertNotEqual(historia_sin_info, historia_con_info)
        
        print(f"\nHistoria sin info personal: {historia_sin_info}")
        print(f"\nHistoria con info personal: {historia_con_info}")

    def test_generar_excusa_para_no_contar(self):
        """Prueba la generación de excusas."""
        excusa = generar_excusa_para_no_contar()
        self.assertIsNotNone(excusa)
        self.assertIsInstance(excusa, str)
        self.assertTrue(len(excusa) > 0)
        
        print(f"\nExcusa generada: {excusa}")

    def test_decidir_contar_historia(self):
        """Prueba la decisión de contar o no una historia."""
        # Fijar la semilla para reproducibilidad
        random.seed(42)
        
        # Realizar múltiples pruebas para verificar la distribución
        decisiones = [decidir_contar_historia() for _ in range(100)]
        
        # Verificar que hay una mezcla de True y False
        self.assertTrue(any(decisiones))
        self.assertTrue(not all(decisiones))
        
        # Verificar que la proporción es aproximadamente 70% True
        proporcion_true = sum(decisiones) / len(decisiones)
        self.assertAlmostEqual(proporcion_true, 0.7, delta=0.15)
        
        print(f"\nProporción de decisiones positivas: {proporcion_true:.2f}")

if __name__ == "__main__":
    unittest.main()
