import json
import os
import random

# Importar configuración
from src.config.settings import INFO_PERSONAL_ARCHIVO

# Función para cargar la información personal desde el archivo JSON
def cargar_info_personal():
    try:
        if os.path.exists(INFO_PERSONAL_ARCHIVO):
            with open(INFO_PERSONAL_ARCHIVO, 'r', encoding='utf-8') as archivo:
                info_personal = json.load(archivo)
                print(f"✅ Información personal cargada correctamente")
                return info_personal
        else:
            print(f"⚠️ No se encontró el archivo de información personal: {INFO_PERSONAL_ARCHIVO}")
            return {}
    except Exception as e:
        print(f"❌ Error al cargar información personal: {e}")
        return {}

# Función para generar un resumen de la información personal para el prompt
def generar_contexto_personal(info_personal):
    if not info_personal:
        return ""

    try:
        contexto = "INFORMACIÓN PERSONAL (USA ESTOS DATOS PARA RESPONDER DE FORMA COHERENTE):\n\n"

        # Información básica
        if "informacion_basica" in info_personal:
            info_basica = info_personal["informacion_basica"]
            contexto += "DATOS BÁSICOS:\n"
            for clave, valor in info_basica.items():
                contexto += f"- {clave.replace('_', ' ').title()}: {valor}\n"
            contexto += "\n"

        # Personalidad
        if "personalidad" in info_personal:
            personalidad = info_personal["personalidad"]
            contexto += "PERSONALIDAD:\n"

            if "rasgos_principales" in personalidad:
                contexto += "- Rasgos: " + ", ".join(personalidad["rasgos_principales"]) + "\n"

            if "intereses" in personalidad:
                contexto += "- Intereses: " + ", ".join(personalidad["intereses"]) + "\n"

            if "valores" in personalidad:
                contexto += "- Valores: " + ", ".join(personalidad["valores"]) + "\n"

            contexto += "\n"

        # Estilo de comunicación
        if "estilo_comunicacion" in info_personal:
            estilo = info_personal["estilo_comunicacion"]
            contexto += "ESTILO DE COMUNICACIÓN:\n"

            if "expresiones_favoritas" in estilo:
                contexto += "- Expresiones habituales: " + ", ".join(estilo["expresiones_favoritas"]) + "\n"

            if "temas_preferidos" in estilo:
                contexto += "- Temas preferidos: " + ", ".join(estilo["temas_preferidos"]) + "\n"

            if "temas_evitados" in estilo:
                contexto += "- Temas que evito: " + ", ".join(estilo["temas_evitados"]) + "\n"

            if "emojis_frecuentes" in estilo:
                contexto += "- Emojis frecuentes: " + " ".join(estilo["emojis_frecuentes"]) + "\n"

            contexto += "\n"

        # Relaciones
        if "relaciones" in info_personal:
            relaciones = info_personal["relaciones"]
            contexto += "RELACIONES IMPORTANTES:\n"

            # Familia
            if "familia" in relaciones:
                familia = relaciones["familia"]

                if "padres" in familia:
                    padres = familia["padres"]
                    if "padre" in padres:
                        padre = padres["padre"]
                        contexto += f"- Padre: {padre['nombre']} ({padre['profesion']}). {padre['relacion']}\n"

                    if "madre" in padres:
                        madre = padres["madre"]
                        contexto += f"- Madre: {madre['nombre']} ({madre['profesion']}). {madre['relacion']}\n"

                if "hermanos" in familia and familia["hermanos"]:
                    for hermano in familia["hermanos"]:
                        contexto += f"- Hermano/a: {hermano['nombre']} ({hermano['edad']} años). {hermano['relacion']}\n"

            # Pareja
            if "pareja" in relaciones and relaciones["pareja"]:
                pareja = relaciones["pareja"]
                contexto += f"- Pareja: {pareja['nombre']} (relación de {pareja['tiempo_relacion']}). {pareja['frecuencia_contacto']}\n"

            # Amigos cercanos (solo mencionar 1-2 para no sobrecargar)
            if "amigos_cercanos" in relaciones and relaciones["amigos_cercanos"]:
                amigos = relaciones["amigos_cercanos"]
                contexto += "- Amigos cercanos: "
                amigos_info = []

                for i, amigo in enumerate(amigos[:2]):  # Solo los 2 primeros
                    amigos_info.append(f"{amigo['nombre']} ({amigo['apodo']})")

                contexto += ", ".join(amigos_info) + "\n"

            contexto += "\n"

        # Trabajo
        if "trabajo" in info_personal:
            trabajo = info_personal["trabajo"]
            contexto += "TRABAJO:\n"
            contexto += f"- Posición: {trabajo.get('posicion_actual', 'No especificado')} en {trabajo.get('empresa_actual', 'No especificado')}\n"
            contexto += f"- Modalidad: {trabajo.get('modalidad', 'No especificado')}\n"

            if "tecnologias" in trabajo:
                contexto += f"- Tecnologías: {', '.join(trabajo['tecnologias'])}\n"

            contexto += "\n"

        # Preferencias (resumidas)
        if "preferencias" in info_personal:
            preferencias = info_personal["preferencias"]
            contexto += "PREFERENCIAS:\n"

            if "comida" in preferencias:
                comida = preferencias["comida"]
                if "favoritas" in comida:
                    contexto += f"- Comidas favoritas: {', '.join(comida['favoritas'][:3])}\n"

            if "entretenimiento" in preferencias:
                entretenimiento = preferencias["entretenimiento"]

                if "series_favoritas" in entretenimiento:
                    contexto += f"- Series favoritas: {', '.join(entretenimiento['series_favoritas'][:3])}\n"

                if "videojuegos_favoritos" in entretenimiento:
                    contexto += f"- Videojuegos favoritos: {', '.join(entretenimiento['videojuegos_favoritos'][:3])}\n"

                if "musica" in entretenimiento and "artistas_favoritos" in entretenimiento["musica"]:
                    contexto += f"- Música: {', '.join(entretenimiento['musica']['artistas_favoritos'][:3])}\n"

        return contexto
    except Exception as e:
        print(f"❌ Error al generar contexto personal: {e}")
        return ""

# Función para obtener respuestas específicas basadas en la información personal
def obtener_respuesta_personal(mensaje, info_personal):
    """
    Genera respuestas específicas basadas en la información personal cuando
    se hacen preguntas directas sobre datos personales.
    """
    if not info_personal:
        return None

    mensaje_lower = mensaje.lower()

    # Preguntas sobre información básica
    if any(frase in mensaje_lower for frase in ["cómo te llamas", "cuál es tu nombre", "quién eres"]):
        return f"Me llamo {info_personal.get('informacion_basica', {}).get('nombre', 'Ian')}"

    if any(frase in mensaje_lower for frase in ["cuántos años tienes", "qué edad tienes"]):
        return f"Tengo {info_personal.get('informacion_basica', {}).get('edad', '28')} años"

    if any(frase in mensaje_lower for frase in ["dónde vives", "dónde estás viviendo", "en qué ciudad vives"]):
        return f"Vivo en {info_personal.get('informacion_basica', {}).get('ciudad_actual', 'Madrid')}"

    if any(frase in mensaje_lower for frase in ["a qué te dedicas", "en qué trabajas", "cuál es tu trabajo"]):
        trabajo = info_personal.get('trabajo', {})
        return f"Soy {trabajo.get('posicion_actual', 'desarrollador')} en {trabajo.get('empresa_actual', 'una empresa de tecnología')}"

    # Preguntas sobre relaciones
    if any(frase in mensaje_lower for frase in ["tienes novia", "tienes pareja", "estás con alguien"]):
        pareja = info_personal.get('relaciones', {}).get('pareja', {})
        if pareja:
            return f"Sí, estoy con {pareja.get('nombre', 'Sara')} desde hace {pareja.get('tiempo_relacion', 'un tiempo')}"
        else:
            return "No, ahora mismo estoy soltero"

    if any(frase in mensaje_lower for frase in ["tienes hermanos", "cuántos hermanos tienes"]):
        hermanos = info_personal.get('relaciones', {}).get('familia', {}).get('hermanos', [])
        if hermanos:
            if len(hermanos) == 1:
                return f"Tengo una hermana que se llama {hermanos[0].get('nombre', 'Laura')}"
            else:
                return f"Tengo {len(hermanos)} hermanos"
        else:
            return "No, soy hijo único"

    # Preguntas sobre gustos y preferencias
    if any(frase in mensaje_lower for frase in ["qué música te gusta", "qué tipo de música escuchas"]):
        musica = info_personal.get('preferencias', {}).get('entretenimiento', {}).get('musica', {})
        if musica:
            artistas = musica.get('artistas_favoritos', [])
            generos = musica.get('generos', [])
            if artistas:
                return f"Me gusta {', '.join(artistas[:2])}, sobre todo {random.choice(generos)}"
            elif generos:
                return f"Me gusta el {' y el '.join(generos[:2])}"
        return "Me gusta bastante el rock y la electrónica"

    if any(frase in mensaje_lower for frase in ["qué videojuegos juegas", "a qué juegas", "qué juegos te gustan"]):
        juegos = info_personal.get('preferencias', {}).get('entretenimiento', {}).get('videojuegos_favoritos', [])
        if juegos:
            return f"Últimamente estoy jugando a {random.choice(juegos)}"
        return "Juego principalmente RPGs y juegos de estrategia"

    # Si no hay una respuesta específica, devolver None para usar el modelo de IA
    return None
