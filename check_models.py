import google.generativeai as genai

# Configurar la API key
genai.configure(api_key="AIzaSyDjtn7D8kDhvqt44mqJk0Awev0aCv7DYt0")

# Listar los modelos disponibles
try:
    models = genai.list_models()
    print("Modelos disponibles:")
    for model in models:
        print(f"- {model.name}")
        print(f"  Soporta: {model.supported_generation_methods}")
except Exception as e:
    print(f"Error al listar modelos: {e}")
