<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Bot - Interfaz de Prueba</title>
    <!-- Biblioteca para convertir emojis en formato texto a gráficos -->
    <script src="https://cdn.jsdelivr.net/npm/emoji-toolkit@7.0.0/lib/js/joypixels.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/emoji-toolkit@7.0.0/extras/css/joypixels.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f2f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .chat-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: 80vh;
            max-height: 700px;
        }
        .chat-header {
            background-color: #128C7E;
            color: white;
            padding: 15px;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
            background-color: #E5DDD5;
            background-image: url('https://i.pinimg.com/originals/97/c0/07/97c00759d90d786d9b6a65364a23e927.jpg');
            background-size: contain;
            background-blend-mode: overlay;
        }
        .message {
            max-width: 70%;
            padding: 10px 12px;
            margin-bottom: 10px;
            border-radius: 8px;
            position: relative;
            word-wrap: break-word;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            line-height: 1.4;
        }
        .message.user {
            background-color: #DCF8C6;
            align-self: flex-end;
            margin-left: auto;
            border-top-right-radius: 0;
        }
        .message.bot {
            background-color: white;
            align-self: flex-start;
            border-top-left-radius: 0;
        }
        .message .time {
            font-size: 0.7em;
            color: #999;
            text-align: right;
            margin-top: 3px;
        }
        .chat-input {
            display: flex;
            padding: 10px;
            background-color: #F0F0F0;
            border-top: 1px solid #E0E0E0;
        }
        .chat-input input {
            flex: 1;
            padding: 12px 15px;
            border: none;
            border-radius: 20px;
            margin-right: 10px;
            font-size: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .chat-input input:focus {
            outline: none;
            box-shadow: 0 1px 5px rgba(0,0,0,0.2);
        }
        .chat-input button {
            background-color: #128C7E;
            color: white;
            border: none;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .chat-input button:hover {
            background-color: #075E54;
        }
        .chat-input button:disabled {
            background-color: #ccc;
        }
        .actions {
            margin-top: 15px;
            display: flex;
            justify-content: space-between;
        }
        .actions button {
            background-color: #128C7E;
            color: white;
            border: none;
            padding: 10px 18px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.2s, transform 0.1s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .actions button:hover {
            background-color: #075E54;
        }
        .actions button:active {
            transform: scale(0.98);
        }
        .emoji {
            font-style: normal;
            margin-right: 5px;
        }
        .debug-panel {
            margin-top: 15px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 150px;
            overflow-y: auto;
            display: none;
        }
        .debug-toggle {
            margin-top: 10px;
            text-align: center;
            font-size: 12px;
            color: #666;
            cursor: pointer;
        }
        .debug-entry {
            margin-bottom: 5px;
            padding-bottom: 5px;
            border-bottom: 1px solid #ddd;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        .typing-indicator {
            display: none;
            background-color: white;
            padding: 10px 15px;
            border-radius: 10px;
            border-top-left-radius: 0;
            margin-bottom: 10px;
            width: fit-content;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        .typing-indicator span {
            height: 9px;
            width: 9px;
            background-color: #128C7E;
            display: inline-block;
            border-radius: 50%;
            animation: typing 1.2s infinite;
            margin: 0 2px;
            opacity: 0.8;
        }
        .typing-indicator span:nth-child(2) {
            animation-delay: 0.2s;
        }
        .typing-indicator span:nth-child(3) {
            animation-delay: 0.4s;
        }
        @keyframes typing {
            0% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
            100% { transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="chat-container">
            <div class="chat-header">
                <span><i class="emoji">💬</i> WhatsApp Bot - Interfaz de Prueba</span>
                <span id="chat-id">Chat ID: test_chat</span>
            </div>
            <div class="chat-messages" id="messages">
                <!-- Los mensajes se cargarán aquí -->
                <div class="typing-indicator" id="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
            <form id="chat-form" class="chat-input">
                <input type="text" id="message-input" placeholder="Escribe un mensaje... (presiona Enter para enviar)" autocomplete="off">
                <button type="submit" id="send-button" title="Enviar mensaje">➤</button>
            </form>
        </div>
        <div class="actions">
            <button id="clear-button" type="button">Borrar historial</button>
            <button id="change-chat-button" type="button">Cambiar Chat ID</button>
        </div>
        <div class="debug-toggle" id="debug-toggle">Mostrar panel de depuración</div>
        <div class="debug-panel" id="debug-panel"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const messagesContainer = document.getElementById('messages');
            const chatForm = document.getElementById('chat-form');
            const messageInput = document.getElementById('message-input');
            const sendButton = document.getElementById('send-button');
            const clearButton = document.getElementById('clear-button');
            const changeButton = document.getElementById('change-chat-button');
            const chatIdDisplay = document.getElementById('chat-id');
            const typingIndicator = document.getElementById('typing-indicator');

            let chatId = 'test_chat';

            // Función para agregar entradas al log de depuración
            function addDebugLog(message, type = 'info') {
                const entry = document.createElement('div');
                entry.className = `debug-entry ${type}`;
                entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
                debugPanel.appendChild(entry);
                debugPanel.scrollTop = debugPanel.scrollHeight;
            }

            // Alternar panel de depuración
            debugToggle.addEventListener('click', function() {
                if (debugPanel.style.display === 'block') {
                    debugPanel.style.display = 'none';
                    debugToggle.textContent = 'Mostrar panel de depuración';
                } else {
                    debugPanel.style.display = 'block';
                    debugToggle.textContent = 'Ocultar panel de depuración';
                }
            });

            // Cargar historial inicial
            loadHistory();

            // Evento para enviar mensaje (usando el formulario)
            chatForm.addEventListener('submit', function(e) {
                e.preventDefault(); // Prevenir el envío del formulario
                console.log('Formulario enviado');
                sendMessage();
            });

            // Enfocar el campo de entrada al cargar la página
            messageInput.focus();

            // Evento para borrar historial
            clearButton.addEventListener('click', clearHistory);

            // Evento para cambiar Chat ID
            changeButton.addEventListener('click', function() {
                const newChatId = prompt('Introduce un nuevo Chat ID:', chatId);
                if (newChatId && newChatId.trim() !== '') {
                    chatId = newChatId.trim();
                    chatIdDisplay.textContent = 'Chat ID: ' + chatId;
                    loadHistory();
                }
            });

            function sendMessage() {
                const message = messageInput.value.trim();
                if (!message) return;

                // Añadir mensaje del usuario a la interfaz
                addMessage('user', message, getCurrentTime());
                messageInput.value = '';

                // Volver a enfocar el campo de entrada para continuar escribiendo
                messageInput.focus();

                // Mostrar indicador de escritura
                typingIndicator.style.display = 'block';
                messagesContainer.scrollTop = messagesContainer.scrollHeight;

                // Deshabilitar botón mientras se procesa
                sendButton.disabled = true;

                // Enviar mensaje al servidor
                addDebugLog(`Enviando mensaje: "${message}" a chat_id: ${chatId}`);

                // Usar siempre la URL completa para evitar problemas de CORS
                const url = 'http://127.0.0.1:5000/enviar_mensaje';

                addDebugLog(`URL de la solicitud: ${url}`);

                // Preparar los datos para enviar
                const requestData = {
                    mensaje: message,
                    chat_id: chatId
                };

                addDebugLog(`Datos de la solicitud: ${JSON.stringify(requestData)}`);

                // Realizar la solicitud fetch
                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                })
                .then(response => {
                    addDebugLog(`Respuesta recibida: ${response.status} ${response.statusText}`);
                    if (!response.ok) {
                        throw new Error(`Error HTTP: ${response.status} ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    addDebugLog(`Datos recibidos: ${JSON.stringify(data)}`, 'success');

                    // Ocultar indicador de escritura
                    typingIndicator.style.display = 'none';

                    // Añadir respuesta del bot
                    if (data.respuesta) {
                        addMessage('bot', data.respuesta, data.timestamp);
                    } else if (data.error) {
                        throw new Error(data.error);
                    }

                    // Habilitar botón de nuevo
                    sendButton.disabled = false;

                    // Volver a enfocar el campo de entrada después de recibir la respuesta
                    messageInput.focus();
                })
                .catch(error => {
                    addDebugLog(`Error: ${error.message}`, 'error');
                    typingIndicator.style.display = 'none';
                    sendButton.disabled = false;

                    // Volver a enfocar el campo de entrada incluso en caso de error
                    messageInput.focus();

                    // Mostrar mensaje de error más detallado
                    let errorMsg = 'Error al enviar mensaje: ' + error;
                    if (error.message && error.message.includes('Failed to fetch')) {
                        errorMsg = 'Error de conexión con el servidor. Asegúrate de que el servidor esté en ejecución en http://127.0.0.1:5000';
                    }

                    // Añadir mensaje de error en el chat
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'message bot';
                    errorDiv.style.backgroundColor = '#ffdddd';

                    const errorContent = document.createElement('p');
                    errorContent.textContent = errorMsg;
                    errorContent.style.margin = '0';
                    errorContent.style.color = '#cc0000';

                    errorDiv.appendChild(errorContent);
                    messagesContainer.insertBefore(errorDiv, typingIndicator);

                    // Scroll al final
                    messagesContainer.scrollTop = messagesContainer.scrollHeight;
                });
            }

            function loadHistory() {
                // Limpiar mensajes actuales
                messagesContainer.innerHTML = '';
                messagesContainer.appendChild(typingIndicator);

                addDebugLog(`Cargando historial para chat_id: ${chatId}`);

                // Cargar historial del servidor
                // Usar siempre la URL completa para evitar problemas de CORS
                fetch(`http://127.0.0.1:5000/obtener_historial?chat_id=${chatId}`, {
                    headers: {
                        'Accept': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    addDebugLog(`Historial recibido: ${data.mensajes ? data.mensajes.length : 0} mensajes`);

                    if (data.mensajes && data.mensajes.length > 0) {
                        data.mensajes.forEach(msg => {
                            addMessage(
                                msg.role === 'user' ? 'user' : 'bot',
                                msg.content,
                                msg.timestamp
                            );
                        });
                    }
                })
                .catch(error => {
                    addDebugLog(`Error al cargar historial: ${error.message}`, 'error');
                });
            }

            function clearHistory() {
                if (confirm('¿Estás seguro de que quieres borrar todo el historial de este chat?')) {
                    addDebugLog(`Borrando historial para chat_id: ${chatId}`);

                    // Usar siempre la URL completa para evitar problemas de CORS
                    fetch('http://127.0.0.1:5000/borrar_historial', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify({
                            chat_id: chatId
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        addDebugLog(`Respuesta al borrar historial: ${JSON.stringify(data)}`);
                        if (data.success) {
                            messagesContainer.innerHTML = '';
                            messagesContainer.appendChild(typingIndicator);
                            alert('Historial borrado correctamente');
                        } else {
                            alert('Error al borrar historial: ' + data.error);
                        }
                    })
                    .catch(error => {
                        addDebugLog(`Error al borrar historial: ${error.message}`, 'error');
                        alert('Error al borrar historial: ' + error);
                    });
                }
            }

            function addMessage(role, content, time) {
                // Registrar en el log de depuración
                addDebugLog(`Añadiendo mensaje - Rol: ${role}, Contenido: "${content}", Hora: ${time || getCurrentTime()}`);

                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${role}`;

                const contentP = document.createElement('p');
                // Usar innerHTML en lugar de textContent para permitir emojis
                // Primero sanitizamos el contenido para evitar XSS
                const sanitizedContent = content
                    .replace(/&/g, '&amp;')
                    .replace(/</g, '&lt;')
                    .replace(/>/g, '&gt;')
                    .replace(/"/g, '&quot;')
                    .replace(/'/g, '&#039;');

                // Convertir emojis en formato :emoji: a su representación gráfica
                contentP.innerHTML = sanitizedContent;
                contentP.style.margin = '0';

                const timeSpan = document.createElement('div');
                timeSpan.className = 'time';
                timeSpan.textContent = time || getCurrentTime();

                messageDiv.appendChild(contentP);
                messageDiv.appendChild(timeSpan);

                // Asegurarse de que el indicador de escritura esté en el contenedor
                if (!messagesContainer.contains(typingIndicator)) {
                    messagesContainer.appendChild(typingIndicator);
                }

                // Insertar antes del indicador de escritura
                messagesContainer.insertBefore(messageDiv, typingIndicator);

                // Scroll al final
                messagesContainer.scrollTop = messagesContainer.scrollHeight;

                // Registrar que el mensaje se ha añadido correctamente
                addDebugLog(`Mensaje añadido correctamente - ID: ${messageDiv.id}`, 'success');
            }

            function getCurrentTime() {
                const now = new Date();
                return now.getHours().toString().padStart(2, '0') + ':' +
                       now.getMinutes().toString().padStart(2, '0');
            }

            // Mensaje inicial
            addDebugLog('Interfaz de chat cargada correctamente');
        });
    </script>
</body>
</html>
