<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> <PERSON>pp</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f0f0f0;
        }
        h1 {
            text-align: center;
            color: #128C7E;
        }
        .chat-container {
            background-color: #DCF8C6;
            border-radius: 10px;
            padding: 10px;
            margin-bottom: 20px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #128C7E;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 7px;
            max-width: 80%;
            word-wrap: break-word;
        }
        .user-message {
            background-color: #ffffff;
            margin-left: auto;
            margin-right: 0;
            text-align: right;
        }
        .bot-message {
            background-color: #E2F7CB;
            margin-right: auto;
            margin-left: 0;
        }
        .input-container {
            display: flex;
            margin-top: 10px;
        }
        #message-input {
            flex-grow: 1;
            padding: 10px;
            border: 1px solid #128C7E;
            border-radius: 5px 0 0 5px;
            outline: none;
        }
        #send-button {
            background-color: #128C7E;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 0 5px 5px 0;
            cursor: pointer;
        }
        #send-button:hover {
            background-color: #075E54;
        }
        .timestamp {
            font-size: 0.7em;
            color: #888;
            margin-top: 3px;
        }
        .clear-button {
            background-color: #FF5252;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 10px;
        }
        .clear-button:hover {
            background-color: #D32F2F;
        }
    </style>
</head>
<body>
    <h1>Probador de Bot de WhatsApp</h1>

    <div style="margin-bottom: 15px; background-color: #E1F5FE; padding: 10px; border-radius: 5px; border: 1px solid #0288D1;">
        <details>
            <summary style="cursor: pointer; font-weight: bold; color: #0288D1;">Comandos disponibles</summary>
            <ul style="margin-top: 10px; padding-left: 20px;">
                <li><code>/ayuda</code> - Muestra la lista de comandos</li>
                <li><code>/status</code> - Muestra el estado del bot</li>
                <li><code>/borrar</code> - Borra el historial de esta conversación</li>
                <li><code>/silencio</code> - Activa el modo silencioso (no responde)</li>
                <li><code>/hablar</code> - Desactiva el modo silencioso</li>
                <li><code>/debug</code> - Activa/desactiva el modo debug</li>
                <li><code>/info</code> - Muestra información sobre el bot</li>
            </ul>
        </details>
    </div>

    <button id="clear-chat" class="clear-button">Borrar conversación</button>

    <div class="chat-container" id="chat-container">
        {% for message in messages %}
            {% if message.role == 'user' %}
                <div class="message user-message">
                    {{ message.content }}
                    <div class="timestamp">{{ message.timestamp }}</div>
                </div>
            {% else %}
                <div class="message bot-message">
                    {{ message.content }}
                    <div class="timestamp">{{ message.timestamp }}</div>
                </div>
            {% endif %}
        {% endfor %}
    </div>

    <form id="message-form" action="/send" method="post">
        <div class="input-container">
            <input type="text" id="message-input" name="message" placeholder="Escribe un mensaje..." autocomplete="off" required>
            <button type="submit" id="send-button">Enviar</button>
        </div>
    </form>

    <script>
        // Scroll al final del chat al cargar la página
        window.onload = function() {
            var chatContainer = document.getElementById('chat-container');
            chatContainer.scrollTop = chatContainer.scrollHeight;
        };

        // Manejar el envío del formulario con AJAX
        document.getElementById('message-form').addEventListener('submit', function(e) {
            e.preventDefault();

            var messageInput = document.getElementById('message-input');
            var message = messageInput.value.trim();

            if (message) {
                // Crear elemento de mensaje del usuario
                var userMessage = document.createElement('div');
                userMessage.className = 'message user-message';
                userMessage.textContent = message;

                var timestamp = document.createElement('div');
                timestamp.className = 'timestamp';
                var now = new Date();
                timestamp.textContent = now.toLocaleString();
                userMessage.appendChild(timestamp);

                document.getElementById('chat-container').appendChild(userMessage);

                // Limpiar el input
                messageInput.value = '';

                // Scroll al final del chat
                var chatContainer = document.getElementById('chat-container');
                chatContainer.scrollTop = chatContainer.scrollHeight;

                // Enviar mensaje al servidor
                fetch('/send', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({message: message}),
                })
                .then(response => response.json())
                .then(data => {
                    // Verificar si estamos en modo silencioso
                    if (!data.silent_mode) {
                        // Crear elemento de mensaje del bot
                        var botMessage = document.createElement('div');
                        botMessage.className = 'message bot-message';

                        // Si el mensaje contiene formato markdown (comandos), procesarlo
                        if (data.response.includes('*') || data.response.includes('•')) {
                            // Reemplazar asteriscos con etiquetas de negrita
                            var formattedText = data.response
                                .replace(/\*([^*]+)\*/g, '<strong>$1</strong>')
                                .replace(/•/g, '&bull;')
                                .replace(/\n/g, '<br>');

                            botMessage.innerHTML = formattedText;
                        } else {
                            botMessage.textContent = data.response;
                        }

                        var timestamp = document.createElement('div');
                        timestamp.className = 'timestamp';
                        var now = new Date();
                        timestamp.textContent = now.toLocaleString();
                        botMessage.appendChild(timestamp);

                        document.getElementById('chat-container').appendChild(botMessage);
                    } else {
                        // Mostrar un mensaje de modo silencioso (gris y en cursiva)
                        var silentMessage = document.createElement('div');
                        silentMessage.className = 'message bot-message';
                        silentMessage.style.backgroundColor = '#f0f0f0';
                        silentMessage.style.color = '#888';
                        silentMessage.style.fontStyle = 'italic';
                        silentMessage.textContent = data.response;

                        var timestamp = document.createElement('div');
                        timestamp.className = 'timestamp';
                        var now = new Date();
                        timestamp.textContent = now.toLocaleString();
                        silentMessage.appendChild(timestamp);

                        document.getElementById('chat-container').appendChild(silentMessage);
                    }

                    // Scroll al final del chat
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            }
        });

        // Manejar el botón de borrar chat
        document.getElementById('clear-chat').addEventListener('click', function() {
            if (confirm('¿Estás seguro de que quieres borrar toda la conversación?')) {
                fetch('/clear', {
                    method: 'POST',
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('chat-container').innerHTML = '';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            }
        });
    </script>
</body>
</html>
