"""
Script para probar el bot a través de una interfaz de línea de comandos.
No requiere Flask ni ninguna otra dependencia web.
"""

import os
import sys

# Añadir el directorio raíz al path para poder importar los módulos
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Importar el módulo central del bot
from src.bot.core import inicializar_bot, procesar_mensaje_y_generar_respuesta
# Importar configuración centralizada
from src.config import config
# Importar el módulo de temperatura
from src.utils.temperature import establecer_temperatura, obtener_temperatura

def ejecutar_cli_tester():
    """Ejecuta una interfaz de línea de comandos para probar el bot."""
    print("🤖 Iniciando interfaz de línea de comandos para probar el bot")
    print("📋 Escribe 'salir' para terminar la conversación")
    print("📋 Escribe 'borrar' para borrar el historial")
    print("📋 Escribe '/ayuda' para ver los comandos disponibles")
    print("📋 Escribe 'temp' para ver la temperatura actual")
    print("📋 Escribe 'temp X' para establecer la temperatura (0-100)")
    print("---------------------------------------------------")

    # Mostrar la temperatura actual
    temp_actual = obtener_temperatura()
    temp_porcentaje = int(temp_actual * 100)
    print(f"🌡️ Temperatura actual: {temp_porcentaje}%")

    # Inicializar el bot
    inicializar_bot(config.HISTORIAL_TEST_ARCHIVO)

    # ID de chat para la prueba
    chat_id = "test_cli"

    # Loop principal
    while True:
        # Solicitar mensaje al usuario
        mensaje = input("\n👤 Tú: ")

        # Verificar si el usuario quiere salir
        if mensaje.lower() in ["salir", "exit", "quit", "q"]:
            print("🤖 Adiós! 👋")
            break

        # Verificar si el usuario quiere borrar el historial
        if mensaje.lower() == "borrar":
            from src.bot.core import historial_conversaciones, guardar_historial
            if chat_id in historial_conversaciones:
                historial_conversaciones[chat_id] = []
                guardar_historial(config.HISTORIAL_TEST_ARCHIVO)
                print("🗑️ Historial borrado correctamente")
            continue

        # Verificar si el usuario quiere ver o cambiar la temperatura
        if mensaje.lower().startswith("temp"):
            partes = mensaje.split()

            # Si solo es "temp", mostrar la temperatura actual
            if len(partes) == 1:
                temp_actual = obtener_temperatura()
                temp_porcentaje = int(temp_actual * 100)
                print(f"""🌡️ *Temperatura de IA:* {temp_porcentaje}%
• 0% = Estricta adherencia a la personalidad
• 100% = Máxima creatividad y libertad
• Actual: {temp_porcentaje}%

Usa 'temp [0-100]' para cambiar la temperatura.""")
                continue

            # Si es "temp X", establecer la temperatura
            elif len(partes) == 2:
                try:
                    # Convertir el valor a entero y luego a float entre 0.0 y 1.0
                    temp_valor = int(partes[1])
                    if 0 <= temp_valor <= 100:
                        nueva_temp = establecer_temperatura(temp_valor / 100.0)
                        temp_porcentaje = int(nueva_temp * 100)
                        print(f"""✅ *Temperatura de IA establecida a {temp_porcentaje}%*
• 0% = Estricta adherencia a la personalidad
• 100% = Máxima creatividad y libertad
• Nueva: {temp_porcentaje}%""")
                    else:
                        print("❌ El valor de temperatura debe estar entre 0 y 100")
                except ValueError:
                    print("❌ El valor de temperatura debe ser un número entero entre 0 y 100")
                continue

        # Procesar el mensaje y obtener la respuesta
        respuesta = procesar_mensaje_y_generar_respuesta(mensaje, chat_id, config.HISTORIAL_TEST_ARCHIVO)

        # Mostrar la respuesta (puede ser una sola respuesta o una lista de respuestas)
        if respuesta:
            if isinstance(respuesta, list):
                # Si es una lista de respuestas, mostrarlas secuencialmente
                for i, resp in enumerate(respuesta):
                    # Pequeña pausa entre mensajes para simular escritura
                    if i > 0:
                        import time
                        time.sleep(config.TIEMPO_BASE_ENTRE_MENSAJES + min(len(resp) * config.FACTOR_TIEMPO_POR_CARACTER, config.TIEMPO_MAXIMO_ADICIONAL))  # Pausa proporcional a la longitud
                    print(f"🤖 Bot: {resp}")
            else:
                # Si es una sola respuesta
                print(f"🤖 Bot: {respuesta}")
        else:
            print("🤖 Bot: (sin respuesta)")

if __name__ == "__main__":
    ejecutar_cli_tester()
