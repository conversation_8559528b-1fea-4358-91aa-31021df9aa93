"""
Configuración centralizada para el bot de WhatsApp con Gemini.
Este archivo contiene todas las variables de configuración importantes del bot.
"""

import os
import pathlib

#######################
# RUTAS Y DIRECTORIOS #
#######################

# Directorio base del proyecto
BASE_DIR = pathlib.Path(__file__).parent.parent.parent.absolute()

# Directorio de datos
DATA_DIR = os.path.join(BASE_DIR, "src", "data")

# Asegurarse de que el directorio de datos exista
os.makedirs(DATA_DIR, exist_ok=True)

# Directorio para el perfil de Chrome (WhatsApp Web)
USER_DATA_DIR = os.path.join(BASE_DIR, "chrome_profile")

#######################
# ARCHIVOS DE DATOS   #
#######################

# Archivos de historial de conversaciones
HISTORIAL_ARCHIVO = os.path.join(DATA_DIR, "historial_conversaciones.json")
HISTORIAL_TEST_ARCHIVO = os.path.join(DATA_DIR, "historial_conversaciones_test.json")

# Archivo de información personal
INFO_PERSONAL_ARCHIVO = os.path.join(DATA_DIR, "info_personal.json")

# Archivos de vocabulario y conocimiento
VOCABULARIO_ARCHIVO = os.path.join(DATA_DIR, "vocabulario.json")
BASE_CONOCIMIENTO_ARCHIVO = os.path.join(DATA_DIR, "base_conocimiento.json")

#######################
# API DE GEMINI       #
#######################

# Clave de API de Gemini
GEMINI_API_KEY = "AIzaSyDjtn7D8kDhvqt44mqJk0Awev0aCv7DYt0"

# Modelo de Gemini a utilizar
GEMINI_MODEL = "gemini-1.5-pro"

#######################
# CONFIGURACIÓN BOT   #
#######################

# Número máximo de mensajes a mantener en el historial por chat
MAX_MENSAJES_HISTORIAL = 50

# Modo silencioso por defecto (no responde a mensajes)
MODO_SILENCIOSO_DEFAULT = False

# Modo debug por defecto (muestra información adicional)
MODO_DEBUG_DEFAULT = False

# Modo de aprendizaje de vocabulario por defecto
MODO_APRENDIZAJE_VOCABULARIO_DEFAULT = True

#######################
# TIEMPOS DE RESPUESTA #
#######################

# Tiempo de espera entre verificaciones de nuevos mensajes en WhatsApp (segundos)
TIEMPO_VERIFICACION_MENSAJES = 0.5

# Tiempo base para pausas entre mensajes secuenciales (segundos)
TIEMPO_BASE_ENTRE_MENSAJES = 0.3

# Factor de tiempo adicional por carácter para pausas entre mensajes
FACTOR_TIEMPO_POR_CARACTER = 0.01

# Tiempo máximo adicional por mensaje (segundos)
TIEMPO_MAXIMO_ADICIONAL = 0.7

#######################
# PROBABILIDADES      #
#######################

# Probabilidad de dividir una respuesta en múltiples mensajes (0.0 - 1.0)
PROB_DIVIDIR_RESPUESTA = 0.2

# Número mínimo de palabras para considerar dividir una respuesta
MIN_PALABRAS_DIVIDIR = 12

# Probabilidad de añadir error tipográfico y corrección (0.0 - 1.0)
PROB_ERROR_TIPOGRAFICO = 0.05

# Probabilidad de contar una historia cuando el bot "recuerda" algo (0.0 - 1.0)
PROB_CONTAR_HISTORIA = 0.2

# Probabilidad de añadir interrogación al final de mensajes (0.0 - 1.0)
PROB_INTERROGACION = 0.3

#######################
# SERVIDOR WEB (TESTER) #
#######################

# Puerto para el servidor web de prueba
PUERTO_SERVIDOR_WEB = 5000

# Host para el servidor web de prueba
HOST_SERVIDOR_WEB = "0.0.0.0"

# Modo debug para el servidor Flask
FLASK_DEBUG = True

# Clave secreta para la aplicación Flask
FLASK_SECRET_KEY = "clave_secreta_para_el_bot"

#######################
# SELENIUM (WHATSAPP) #
#######################

# URL de WhatsApp Web
WHATSAPP_WEB_URL = "https://web.whatsapp.com"

# Tiempo de espera para cargar WhatsApp Web (segundos)
TIEMPO_CARGA_WHATSAPP = 5

# Opciones de Chrome para WhatsApp
CHROME_OPTIONS = [
    "--start-maximized",      # Iniciar maximizado
    "--disable-extensions",   # Desactivar extensiones
    "--disable-gpu",          # Desactivar aceleración GPU
    "--no-sandbox"            # Desactivar sandbox
]

#######################
# SISTEMA DE ENTRENAMIENTO #
#######################

# Puerto para el servidor web de entrenamiento
PUERTO_SERVIDOR_TRAINING = 5001

# Archivo de historial para sesiones de entrenamiento
HISTORIAL_TRAINING_ARCHIVO = os.path.join(DATA_DIR, "historial_entrenamiento.json")

# Archivo para almacenar el perfil de estilo
PERFIL_ESTILO_ARCHIVO = os.path.join(DATA_DIR, "perfil_estilo.json")

# Archivo para almacenar la memoria contextual
MEMORIA_CONTEXTUAL_ARCHIVO = os.path.join(DATA_DIR, "memoria_contextual.json")

# Número mínimo de mensajes para análisis de estilo
MIN_MENSAJES_ANALISIS = 10

# Número máximo de sesiones de entrenamiento a mantener
MAX_SESIONES_ENTRENAMIENTO = 20

# Peso para nuevos datos de entrenamiento (0.0 - 1.0)
PESO_NUEVOS_DATOS = 0.3

#######################
# CONFIGURACIÓN DE RESPUESTAS #
#######################

# Configuración de longitud de respuestas
MIN_PALABRAS_RESPUESTA_NORMAL = 8  # Mínimo de palabras para respuestas normales
MAX_PALABRAS_RESPUESTA_NORMAL = 20  # Máximo de palabras para respuestas normales
MIN_PALABRAS_RESPUESTA_ELABORADA = 25  # Mínimo de palabras para respuestas elaboradas
MAX_PALABRAS_RESPUESTA_ELABORADA = 45  # Máximo de palabras para respuestas elaboradas
