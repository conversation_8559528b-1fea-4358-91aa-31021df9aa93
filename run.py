"""
Script principal unificado para el bot de WhatsApp con Gemini.
Permite iniciar cualquiera de las interfaces disponibles.
"""

import sys
import os
import argparse

# Importar el módulo de logging
from src.utils.logger import get_logger, log_emoji

# Crear logger
logger = get_logger("main", "main.log")

def main():
    """Función principal para iniciar el bot."""
    # Configurar el parser de argumentos
    parser = argparse.ArgumentParser(description="Bot de WhatsApp con Gemini")

    # Añadir argumentos
    parser.add_argument(
        "interface",
        choices=["whatsapp", "web", "cli", "train"],
        help="Interfaz a utilizar: whatsapp, web, cli o train (entrenamiento)"
    )

    parser.add_argument(
        "--debug",
        action="store_true",
        help="Activar modo debug"
    )

    # Parsear argumentos
    args = parser.parse_args()

    # Mostrar información de inicio
    log_emoji(logger, "🚀", f"Iniciando bot con interfaz: {args.interface}")

    if args.debug:
        log_emoji(logger, "🔍", "Modo debug activado")
        # Configurar nivel de logging a DEBUG
        logger.setLevel("DEBUG")

    # Iniciar la interfaz seleccionada
    if args.interface == "whatsapp":
        log_emoji(logger, "📱", "Iniciando interfaz de WhatsApp")
        from src.interfaces.whatsapp import ejecutar_bot
        ejecutar_bot()

    elif args.interface == "web":
        log_emoji(logger, "🌐", "Iniciando interfaz web")
        from src.interfaces.tester import ejecutar_tester
        ejecutar_tester()

    elif args.interface == "cli":
        log_emoji(logger, "💻", "Iniciando interfaz de línea de comandos")
        from run_cli_tester import ejecutar_cli_tester
        ejecutar_cli_tester()

    elif args.interface == "train":
        log_emoji(logger, "🎓", "Iniciando interfaz de entrenamiento")
        from src.interfaces.trainer import ejecutar_entrenamiento
        ejecutar_entrenamiento()

    else:
        log_emoji(logger, "❌", f"Interfaz no válida: {args.interface}", "ERROR")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        log_emoji(logger, "👋", "Bot detenido por el usuario")
    except Exception as e:
        log_emoji(logger, "❌", f"Error inesperado: {str(e)}", "ERROR")
        logger.exception("Detalle del error:")
        sys.exit(1)
