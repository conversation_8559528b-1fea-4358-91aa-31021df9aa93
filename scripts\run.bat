@echo off
echo ===================================================
echo    <PERSON><PERSON> de WhatsApp con Gemini - Launcher Unificado
echo ===================================================
echo.

cd /d "%~dp0\.."
call .venv\Scripts\activate.bat

echo Selecciona la interfaz a utilizar:
echo.
echo 1. WhatsApp
echo 2. Interfaz Web
echo 3. Interfaz CLI
echo.

set /p opcion="Opción (1-3): "

if "%opcion%"=="1" (
    echo.
    echo Iniciando interfaz de WhatsApp...
    python run.py whatsapp
) else if "%opcion%"=="2" (
    echo.
    echo Iniciando interfaz web...
    python run.py web
) else if "%opcion%"=="3" (
    echo.
    echo Iniciando interfaz CLI...
    python run.py cli
) else (
    echo.
    echo Opción no válida. Por favor, selecciona una opción del 1 al 3.
    pause
    exit /b 1
)

pause
