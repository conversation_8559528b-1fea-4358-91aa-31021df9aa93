"""
Interfaz de prueba para el bot de WhatsApp con Gemini.
Este script proporciona una interfaz web para probar el bot sin necesidad de WhatsApp.
"""

import os
import json
from datetime import datetime
from flask import Flask, request, jsonify, render_template
from flask_cors import CORS  # Importar CORS para permitir solicitudes entre dominios

# Importar el módulo central del bot
from src.bot.core import (
    inicializar_bot,
    procesar_mensaje_y_generar_respuesta
)

# Importar el módulo de temperatura
from src.utils.temperature import (
    establecer_temperatura,
    obtener_temperatura
)

# Importar configuración centralizada
from src.config import config

def crear_app():
    """Crea y configura la aplicación Flask."""
    # Configuración de la aplicación Flask
    app = Flask(__name__, template_folder=os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'templates'))
    app.config['SECRET_KEY'] = config.FLASK_SECRET_KEY

    # Configuración detallada de CORS
    cors = CORS(app, resources={
        r"/*": {
            "origins": ["http://localhost:5000", "http://127.0.0.1:5000", "*"],
            "methods": ["GET", "POST", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization", "Accept"]
        }
    })

    # Añadir encabezados CORS a todas las respuestas
    @app.after_request
    def add_cors_headers(response):
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization,Accept')
        response.headers.add('Access-Control-Allow-Methods', 'GET,POST,OPTIONS')
        return response

    # Ruta principal - Interfaz de chat
    @app.route('/')
    def index():
        return render_template('test_simple.html')

    # Ruta alternativa - Interfaz de chat original
    @app.route('/original')
    def original():
        return render_template('chat.html')

    # Ruta para procesar mensajes
    @app.route('/enviar_mensaje', methods=['POST', 'OPTIONS'])
    def enviar_mensaje():
        # Manejar solicitudes OPTIONS para CORS
        if request.method == 'OPTIONS':
            print("Recibida solicitud OPTIONS para /enviar_mensaje")
            return '', 200

        print(f"Recibida solicitud POST para /enviar_mensaje")
        print(f"Headers: {dict(request.headers)}")

        try:
            # Obtener datos del formulario
            datos = request.get_json()
            print(f"Datos recibidos: {datos}")

            if not datos:
                print("Error: No se recibieron datos JSON válidos")
                return jsonify({'error': 'Datos JSON no válidos'}), 400

            mensaje = datos.get('mensaje', '').strip()
            chat_id = datos.get('chat_id', 'test_chat')

            print(f"Mensaje: '{mensaje}', Chat ID: '{chat_id}'")

            if not mensaje:
                print("Error: Mensaje vacío")
                return jsonify({'error': 'Mensaje vacío'}), 400

            # Procesar el mensaje y generar respuesta usando el módulo central
            print(f"Procesando mensaje con el módulo central...")
            respuesta = procesar_mensaje_y_generar_respuesta(mensaje, chat_id, config.HISTORIAL_TEST_ARCHIVO)
            print(f"Respuesta generada: '{respuesta}'")

            # Verificar si la respuesta es una lista (múltiples mensajes) o un solo mensaje
            timestamp = datetime.now().strftime("%H:%M")

            if isinstance(respuesta, list):
                # Si son múltiples mensajes, devolver el primero y guardar los demás para enviarlos secuencialmente
                print(f"Respuesta múltiple detectada con {len(respuesta)} mensajes")

                # Devolver el primer mensaje inmediatamente
                primer_mensaje = respuesta[0]
                mensajes_adicionales = respuesta[1:]

                response_data = {
                    'respuesta': primer_mensaje if primer_mensaje else "...",
                    'timestamp': timestamp,
                    'tiene_mas_mensajes': len(mensajes_adicionales) > 0,
                    'mensajes_adicionales': mensajes_adicionales,
                    'delays': [config.TIEMPO_BASE_ENTRE_MENSAJES + min(len(msg) * config.FACTOR_TIEMPO_POR_CARACTER, config.TIEMPO_MAXIMO_ADICIONAL) for msg in mensajes_adicionales]  # Calcular delays proporcionales a la longitud
                }
            else:
                # Si es un solo mensaje, devolverlo normalmente
                response_data = {
                    'respuesta': respuesta if respuesta else "...",
                    'timestamp': timestamp,
                    'tiene_mas_mensajes': False
                }

            print(f"Enviando respuesta: {response_data}")
            return jsonify(response_data)
        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            print(f"Error al procesar mensaje: {e}")
            print(f"Traceback: {error_traceback}")
            return jsonify({'error': str(e), 'traceback': error_traceback}), 500

    # Ruta para obtener el historial de mensajes
    @app.route('/obtener_historial', methods=['GET', 'OPTIONS'])
    def obtener_historial():
        # Manejar solicitudes OPTIONS para CORS
        if request.method == 'OPTIONS':
            return '', 200

        chat_id = request.args.get('chat_id', 'test_chat')

        try:
            # Cargar el historial desde el archivo
            if os.path.exists(config.HISTORIAL_TEST_ARCHIVO):
                with open(config.HISTORIAL_TEST_ARCHIVO, 'r', encoding='utf-8') as f:
                    historial = json.load(f)

                    # Obtener mensajes del chat específico
                    mensajes = historial.get(chat_id, [])

                    # Formatear los mensajes para la interfaz
                    mensajes_formateados = []
                    for msg in mensajes:
                        mensajes_formateados.append({
                            'role': msg['role'],
                            'content': msg['content'],
                            'timestamp': msg.get('timestamp', '').split()[1][:5] if 'timestamp' in msg else ''
                        })

                    return jsonify({'mensajes': mensajes_formateados})

            return jsonify({'mensajes': []})
        except Exception as e:
            print(f"Error al obtener historial: {e}")
            return jsonify({'error': str(e), 'mensajes': []}), 500

    # Ruta para borrar el historial
    @app.route('/borrar_historial', methods=['POST', 'OPTIONS'])
    def borrar_historial():
        # Manejar solicitudes OPTIONS para CORS
        if request.method == 'OPTIONS':
            return '', 200

        try:
            datos = request.get_json()
            if not datos:
                return jsonify({'error': 'Datos JSON no válidos'}), 400

            chat_id = datos.get('chat_id', 'test_chat')

            # Cargar el historial actual
            historial = {}
            if os.path.exists(config.HISTORIAL_TEST_ARCHIVO):
                with open(config.HISTORIAL_TEST_ARCHIVO, 'r', encoding='utf-8') as f:
                    historial = json.load(f)

            # Borrar el historial del chat específico
            if chat_id in historial:
                historial[chat_id] = []

                # Guardar el historial actualizado
                with open(config.HISTORIAL_TEST_ARCHIVO, 'w', encoding='utf-8') as f:
                    json.dump(historial, f, ensure_ascii=False, indent=2)

                return jsonify({'success': True, 'message': 'Historial borrado correctamente'})

            return jsonify({'success': True, 'message': 'No hay historial para borrar'})
        except Exception as e:
            print(f"Error al borrar historial: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    # Ruta para obtener la temperatura actual
    @app.route('/get_temperature', methods=['GET', 'OPTIONS'])
    def get_temperature():
        # Manejar solicitudes OPTIONS para CORS
        if request.method == 'OPTIONS':
            return '', 200

        try:
            # Obtener la temperatura actual
            temperatura = obtener_temperatura()
            return jsonify({'temperature': temperatura})
        except Exception as e:
            print(f"Error al obtener temperatura: {e}")
            return jsonify({'error': str(e)}), 500

    # Ruta para establecer la temperatura
    @app.route('/set_temperature', methods=['POST', 'OPTIONS'])
    def set_temperature():
        # Manejar solicitudes OPTIONS para CORS
        if request.method == 'OPTIONS':
            return '', 200

        try:
            # Obtener datos del formulario
            datos = request.get_json()
            if not datos:
                return jsonify({'success': False, 'error': 'Datos JSON no válidos'}), 400

            temperatura = datos.get('temperature', 50)

            # Convertir a float entre 0.0 y 1.0
            temperatura_float = float(temperatura) / 100.0

            # Establecer la temperatura
            nueva_temp = establecer_temperatura(temperatura_float)

            # Convertir a porcentaje para la respuesta
            temp_porcentaje = int(nueva_temp * 100)

            print(f"🌡️ Temperatura establecida a {temp_porcentaje}%")
            return jsonify({'success': True, 'temperature': nueva_temp, 'percentage': temp_porcentaje})
        except Exception as e:
            print(f"Error al establecer temperatura: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    return app

def ejecutar_tester():
    """Función principal para ejecutar la interfaz de prueba."""
    # Inicializar el bot con el módulo central
    inicializar_bot(config.HISTORIAL_TEST_ARCHIVO)

    print("🚀 Iniciando interfaz de prueba del bot...")
    print("📋 Comandos disponibles: /ayuda, /status, /borrar, /silencio, /hablar, /debug, /info, /vocab, /vocab_on, /vocab_off")
    print("📚 Aprendizaje de vocabulario activado. El bot aprenderá palabras y frases de las conversaciones.")
    print(f"🌐 Servidor web disponible en: http://127.0.0.1:{config.PUERTO_SERVIDOR_WEB}")

    # Crear la aplicación Flask
    app = crear_app()

    # Iniciar el servidor
    app.run(debug=config.FLASK_DEBUG, host=config.HOST_SERVIDOR_WEB, port=config.PUERTO_SERVIDOR_WEB)

if __name__ == '__main__':
    ejecutar_tester()
