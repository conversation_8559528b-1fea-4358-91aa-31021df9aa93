"""
Módulo para gestionar el aprendizaje y uso de vocabulario del bot de WhatsApp.
Este módulo permite extraer, almacenar y recuperar vocabulario de las conversaciones.
Incluye análisis avanzado de contexto y estructura sintáctica.
"""

import json
import os
import re
import string
from datetime import datetime
from collections import Counter
import random

# Importar configuración centralizada
from src.config import config

# Importar el analizador de vocabulario
from src.utils.vocabulary_analyzer import (
    analizar_vocabulario,
    guardar_base_conocimiento,
    cargar_base_conocimiento
)

# Palabras comunes que no queremos guardar (stopwords)
STOPWORDS = {
    "a", "al", "algo", "algunas", "algunos", "ante", "antes", "como", "con", "contra",
    "cual", "cuando", "de", "del", "desde", "donde", "durante", "e", "el", "ella",
    "ellas", "ellos", "en", "entre", "era", "erais", "eran", "eras", "eres", "es",
    "esa", "esas", "ese", "eso", "esos", "esta", "estaba", "estabais", "estaban",
    "estabas", "estad", "estada", "estadas", "estado", "estados", "estamos", "estando",
    "estar", "estaremos", "estará", "estarán", "estarás", "estaré", "estaréis",
    "estaría", "estaríais", "estaríamos", "estarían", "estarías", "estas", "este",
    "estemos", "esto", "estos", "estoy", "estuve", "estuviera", "estuvierais",
    "estuvieran", "estuvieras", "estuvieron", "estuviese", "estuvieseis", "estuviesen",
    "estuvieses", "estuvimos", "estuviste", "estuvisteis", "estuviéramos",
    "estuviésemos", "estuvo", "está", "estábamos", "estáis", "están", "estás", "esté",
    "estéis", "estén", "estés", "fue", "fuera", "fuerais", "fueran", "fueras",
    "fueron", "fuese", "fueseis", "fuesen", "fueses", "fui", "fuimos", "fuiste",
    "fuisteis", "fuéramos", "fuésemos", "ha", "habida", "habidas", "habido", "habidos",
    "habiendo", "habremos", "habrá", "habrán", "habrás", "habré", "habréis", "habría",
    "habríais", "habríamos", "habrían", "habrías", "habéis", "había", "habíais",
    "habíamos", "habían", "habías", "han", "has", "hasta", "hay", "haya", "hayamos",
    "hayan", "hayas", "hayáis", "he", "hemos", "hube", "hubiera", "hubierais",
    "hubieran", "hubieras", "hubieron", "hubiese", "hubieseis", "hubiesen", "hubieses",
    "hubimos", "hubiste", "hubisteis", "hubiéramos", "hubiésemos", "hubo", "la", "las",
    "le", "les", "lo", "los", "me", "mi", "mis", "mucho", "muchos", "muy", "más",
    "mí", "mía", "mías", "mío", "míos", "nada", "ni", "no", "nos", "nosotras",
    "nosotros", "nuestra", "nuestras", "nuestro", "nuestros", "o", "os", "otra",
    "otras", "otro", "otros", "para", "pero", "poco", "por", "porque", "que", "quien",
    "quienes", "qué", "se", "sea", "seamos", "sean", "seas", "seremos", "será",
    "serán", "serás", "seré", "seréis", "sería", "seríais", "seríamos", "serían",
    "serías", "seáis", "si", "sido", "siendo", "sin", "sobre", "sois", "somos", "son",
    "soy", "su", "sus", "suya", "suyas", "suyo", "suyos", "sí", "también", "tanto",
    "te", "tendremos", "tendrá", "tendrán", "tendrás", "tendré", "tendréis", "tendría",
    "tendríais", "tendríamos", "tendrían", "tendrías", "tened", "tenemos", "tenga",
    "tengamos", "tengan", "tengas", "tengo", "tengáis", "tenida", "tenidas", "tenido",
    "tenidos", "teniendo", "tenéis", "tenía", "teníais", "teníamos", "tenían", "tenías",
    "ti", "tiene", "tienen", "tienes", "todo", "todos", "tu", "tus", "tuve", "tuviera",
    "tuvierais", "tuvieran", "tuvieras", "tuvieron", "tuviese", "tuvieseis", "tuviesen",
    "tuvieses", "tuvimos", "tuviste", "tuvisteis", "tuviéramos", "tuviésemos", "tuvo",
    "tuya", "tuyas", "tuyo", "tuyos", "tú", "un", "una", "uno", "unos", "vosotras",
    "vosotros", "vuestra", "vuestras", "vuestro", "vuestros", "y", "ya", "yo", "él",
    "éramos"
}

# Vocabulario global
vocabulario_data = {
    "metadata": {
        "ultima_actualizacion": "",
        "total_palabras": 0,
        "chats_procesados": []
    },
    "vocabulario": {},
    "frases": {}
}

# Modo de aprendizaje de vocabulario
modo_aprendizaje = config.MODO_APRENDIZAJE_VOCABULARIO_DEFAULT

def cargar_vocabulario():
    """Carga el vocabulario desde el archivo JSON."""
    global vocabulario_data
    try:
        if os.path.exists(config.VOCABULARIO_ARCHIVO):
            with open(config.VOCABULARIO_ARCHIVO, 'r', encoding='utf-8') as archivo:
                vocabulario_data = json.load(archivo)
                print(f"✅ Vocabulario cargado: {len(vocabulario_data['vocabulario'])} palabras y {len(vocabulario_data['frases'])} frases")
    except Exception as e:
        print(f"❌ Error al cargar vocabulario: {e}")
        # Inicializar con estructura vacía
        vocabulario_data = {
            "metadata": {
                "ultima_actualizacion": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "total_palabras": 0,
                "chats_procesados": []
            },
            "vocabulario": {},
            "frases": {}
        }

def guardar_vocabulario():
    """Guarda el vocabulario en el archivo JSON."""
    try:
        # Actualizar metadatos
        vocabulario_data["metadata"]["ultima_actualizacion"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        vocabulario_data["metadata"]["total_palabras"] = len(vocabulario_data["vocabulario"])

        with open(config.VOCABULARIO_ARCHIVO, 'w', encoding='utf-8') as archivo:
            json.dump(vocabulario_data, archivo, ensure_ascii=False, indent=2)
        print(f"✅ Vocabulario guardado: {len(vocabulario_data['vocabulario'])} palabras y {len(vocabulario_data['frases'])} frases")
    except Exception as e:
        print(f"❌ Error al guardar vocabulario: {e}")

def limpiar_texto(texto):
    """Limpia el texto para extraer palabras relevantes."""
    # Convertir a minúsculas
    texto = texto.lower()

    # Eliminar signos de puntuación
    translator = str.maketrans('', '', string.punctuation)
    texto = texto.translate(translator)

    # Eliminar números
    texto = re.sub(r'\d+', '', texto)

    # Eliminar espacios múltiples
    texto = re.sub(r'\s+', ' ', texto).strip()

    return texto

def extraer_palabras(texto):
    """Extrae palabras relevantes del texto."""
    texto_limpio = limpiar_texto(texto)
    palabras = texto_limpio.split()

    # Filtrar stopwords y palabras muy cortas
    palabras_relevantes = [p for p in palabras if p not in STOPWORDS and len(p) > 2]

    return palabras_relevantes

def extraer_frases(texto, min_palabras=3, max_palabras=8):
    """Extrae frases potencialmente útiles del texto."""
    # Dividir el texto en oraciones
    oraciones = re.split(r'[.!?]+', texto)

    frases_relevantes = []
    for oracion in oraciones:
        oracion = oracion.strip()
        if not oracion:
            continue

        # Limpiar la oración
        oracion_limpia = limpiar_texto(oracion)
        palabras = oracion_limpia.split()

        # Verificar si la oración tiene un número adecuado de palabras
        if min_palabras <= len(palabras) <= max_palabras:
            # Verificar que no sea solo stopwords
            palabras_relevantes = [p for p in palabras if p not in STOPWORDS]
            if len(palabras_relevantes) >= min_palabras // 2:
                frases_relevantes.append(oracion)

    return frases_relevantes

def procesar_mensaje(mensaje, chat_id):
    """Procesa un mensaje para extraer, analizar y almacenar vocabulario."""
    global vocabulario_data, modo_aprendizaje

    # Si el modo de aprendizaje está desactivado, no hacer nada
    if not modo_aprendizaje:
        return

    # Registrar el chat si es nuevo
    if chat_id not in vocabulario_data["metadata"]["chats_procesados"]:
        vocabulario_data["metadata"]["chats_procesados"].append(chat_id)

    # Extraer palabras
    palabras = extraer_palabras(mensaje)

    # Identificar palabras nuevas
    palabras_nuevas = [p for p in palabras if p not in vocabulario_data["vocabulario"]]

    # Actualizar frecuencia de palabras existentes
    for palabra in palabras:
        if palabra in vocabulario_data["vocabulario"]:
            vocabulario_data["vocabulario"][palabra]["frecuencia"] += 1
            vocabulario_data["vocabulario"][palabra]["ultima_vez"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Guardar contexto (últimas apariciones)
            contexto = vocabulario_data["vocabulario"][palabra]["contexto"]
            if mensaje not in contexto:
                contexto.append(mensaje)
                # Mantener solo los últimos 3 contextos
                if len(contexto) > 3:
                    contexto.pop(0)

    # Analizar y almacenar palabras nuevas
    for palabra in palabras_nuevas:
        # Realizar análisis avanzado de la palabra
        analisis = analizar_vocabulario(mensaje, palabra)

        # Almacenar la palabra con su análisis
        vocabulario_data["vocabulario"][palabra] = {
            "frecuencia": 1,
            "primera_vez": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "ultima_vez": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "contexto": [mensaje],
            "chat_id": chat_id,
            "analisis": analisis
        }

        print(f"📚 Nueva palabra analizada: '{palabra}' - {analisis['categoria_gramatical']} ({analisis['tipo_situacion']})")

    # Extraer frases
    frases = extraer_frases(mensaje)

    # Identificar frases nuevas
    frases_nuevas = [f for f in frases if f.lower() not in vocabulario_data["frases"]]

    # Actualizar frases existentes
    for frase in frases:
        frase_key = frase.lower()
        if frase_key in vocabulario_data["frases"]:
            vocabulario_data["frases"][frase_key]["frecuencia"] += 1
            vocabulario_data["frases"][frase_key]["ultima_vez"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Analizar y almacenar frases nuevas
    for frase in frases_nuevas:
        frase_key = frase.lower()

        # Realizar análisis de la frase (si es corta, analizarla como unidad)
        if len(frase.split()) <= 5:
            analisis = analizar_vocabulario(mensaje, frase)
        else:
            # Para frases largas, no hacemos análisis sintáctico completo
            analisis = {
                "tipo_situacion": "frase",
                "registro": "informal",
                "connotacion": "neutral"
            }

        vocabulario_data["frases"][frase_key] = {
            "frase_original": frase,
            "frecuencia": 1,
            "primera_vez": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "ultima_vez": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "chat_id": chat_id,
            "analisis": analisis
        }

        print(f"📚 Nueva frase analizada: '{frase}' ({analisis.get('tipo_situacion', 'general')})")

    # Guardar cambios
    if palabras or frases:
        guardar_vocabulario()

    # Guardar base de conocimiento del analizador
    if palabras_nuevas or frases_nuevas:
        guardar_base_conocimiento(config.BASE_CONOCIMIENTO_ARCHIVO)

def obtener_vocabulario_relevante(mensaje, max_palabras=5, max_frases=2):
    """Obtiene vocabulario relevante para el contexto del mensaje actual."""
    palabras_mensaje = set(extraer_palabras(mensaje))

    # Detectar la situación comunicativa del mensaje actual
    from src.utils.vocabulary_analyzer import detectar_situacion
    situacion_actual = detectar_situacion(mensaje)

    print(f"🔍 Situación detectada en mensaje: {situacion_actual}")

    # Buscar palabras relacionadas por situación y contexto
    palabras_relacionadas = []

    # 1. Primero, buscar palabras con la misma situación comunicativa
    for palabra, datos in vocabulario_data["vocabulario"].items():
        # No sugerir palabras que ya están en el mensaje
        if palabra in palabras_mensaje:
            continue

        # Verificar si la palabra tiene análisis
        if "analisis" in datos and "tipo_situacion" in datos["analisis"]:
            # Si la situación coincide, darle prioridad alta
            if datos["analisis"]["tipo_situacion"] == situacion_actual:
                # Dar mayor peso a palabras en la misma situación (frecuencia * 2)
                palabras_relacionadas.append((palabra, datos["frecuencia"] * 2, "situación"))

    # 2. Luego, buscar palabras que aparecen en contextos similares
    for palabra, datos in vocabulario_data["vocabulario"].items():
        # No sugerir palabras que ya están en el mensaje o ya fueron seleccionadas por situación
        if palabra in palabras_mensaje or any(p[0] == palabra for p in palabras_relacionadas):
            continue

        # Verificar si alguna palabra del mensaje aparece en el contexto de esta palabra
        for contexto in datos["contexto"]:
            if any(p in contexto.lower() for p in palabras_mensaje):
                palabras_relacionadas.append((palabra, datos["frecuencia"], "contexto"))
                break

    # Ordenar por relevancia (frecuencia) y tomar las más relevantes
    palabras_relacionadas.sort(key=lambda x: x[1], reverse=True)
    palabras_sugeridas = [p[0] for p in palabras_relacionadas[:max_palabras]]

    # Si hay palabras relacionadas, mostrar información sobre ellas
    if palabras_relacionadas:
        print(f"📚 Palabras relacionadas encontradas:")
        for palabra, relevancia, tipo in palabras_relacionadas[:max_palabras]:
            if "analisis" in vocabulario_data["vocabulario"][palabra]:
                analisis = vocabulario_data["vocabulario"][palabra]["analisis"]
                print(f"  - '{palabra}' ({analisis.get('categoria_gramatical', 'desconocida')}) - Relevancia por {tipo}")
            else:
                print(f"  - '{palabra}' - Relevancia por {tipo}")

    # Si no hay suficientes palabras relacionadas, agregar algunas de las más frecuentes
    if len(palabras_sugeridas) < max_palabras:
        palabras_frecuentes = [(p, datos["frecuencia"])
                              for p, datos in vocabulario_data["vocabulario"].items()
                              if p not in palabras_sugeridas and p not in palabras_mensaje]
        palabras_frecuentes.sort(key=lambda x: x[1], reverse=True)
        palabras_adicionales = [p[0] for p in palabras_frecuentes[:max_palabras-len(palabras_sugeridas)]]

        if palabras_adicionales:
            print(f"📚 Palabras frecuentes adicionales: {', '.join(palabras_adicionales)}")
            palabras_sugeridas.extend(palabras_adicionales)

    # Buscar frases relevantes por situación y contexto
    frases_relevantes = []

    # 1. Primero, buscar frases con la misma situación comunicativa
    for frase_key, datos in vocabulario_data["frases"].items():
        if "analisis" in datos and "tipo_situacion" in datos["analisis"]:
            if datos["analisis"]["tipo_situacion"] == situacion_actual:
                frases_relevantes.append((datos["frase_original"], datos["frecuencia"] * 2, "situación"))

    # 2. Luego, buscar frases que contienen palabras del mensaje
    for frase_key, datos in vocabulario_data["frases"].items():
        # No incluir frases ya seleccionadas por situación
        if any(f[0] == datos["frase_original"] for f in frases_relevantes):
            continue

        # Verificar si alguna palabra del mensaje aparece en la frase
        if any(p in frase_key for p in palabras_mensaje):
            frases_relevantes.append((datos["frase_original"], datos["frecuencia"], "contenido"))

    # Ordenar por relevancia y tomar las más relevantes
    frases_relevantes.sort(key=lambda x: x[1], reverse=True)
    frases_sugeridas = [f[0] for f in frases_relevantes[:max_frases]]

    # Si hay frases relacionadas, mostrar información sobre ellas
    if frases_relevantes:
        print(f"📚 Frases relacionadas encontradas:")
        for frase, relevancia, tipo in frases_relevantes[:max_frases]:
            print(f"  - '{frase}' - Relevancia por {tipo}")

    return {
        "palabras": palabras_sugeridas,
        "frases": frases_sugeridas,
        "situacion": situacion_actual
    }

def toggle_modo_aprendizaje():
    """Activa o desactiva el modo de aprendizaje de vocabulario."""
    global modo_aprendizaje
    modo_aprendizaje = not modo_aprendizaje
    return modo_aprendizaje

def obtener_estadisticas():
    """Obtiene estadísticas sobre el vocabulario aprendido."""
    total_palabras = len(vocabulario_data["vocabulario"])
    total_frases = len(vocabulario_data["frases"])

    # Contar palabras con análisis
    palabras_con_analisis = sum(1 for datos in vocabulario_data["vocabulario"].values() if "analisis" in datos)
    frases_con_analisis = sum(1 for datos in vocabulario_data["frases"].values() if "analisis" in datos)

    # Contar palabras por categoría gramatical
    categorias = {}
    for datos in vocabulario_data["vocabulario"].values():
        if "analisis" in datos and "categoria_gramatical" in datos["analisis"]:
            categoria = datos["analisis"]["categoria_gramatical"]
            categorias[categoria] = categorias.get(categoria, 0) + 1

    # Contar palabras por tipo de situación
    situaciones = {}
    for datos in vocabulario_data["vocabulario"].values():
        if "analisis" in datos and "tipo_situacion" in datos["analisis"]:
            situacion = datos["analisis"]["tipo_situacion"]
            situaciones[situacion] = situaciones.get(situacion, 0) + 1

    # Palabras más frecuentes
    palabras_frecuentes = [(p, datos["frecuencia"])
                          for p, datos in vocabulario_data["vocabulario"].items()]
    palabras_frecuentes.sort(key=lambda x: x[1], reverse=True)
    top_palabras = palabras_frecuentes[:10]

    # Frases más frecuentes
    frases_frecuentes = [(datos["frase_original"], datos["frecuencia"])
                        for _, datos in vocabulario_data["frases"].items()]
    frases_frecuentes.sort(key=lambda x: x[1], reverse=True)
    top_frases = frases_frecuentes[:5]

    # Obtener las categorías gramaticales más comunes
    top_categorias = sorted(categorias.items(), key=lambda x: x[1], reverse=True)[:5]

    # Obtener las situaciones más comunes
    top_situaciones = sorted(situaciones.items(), key=lambda x: x[1], reverse=True)[:5]

    return {
        "total_palabras": total_palabras,
        "total_frases": total_frases,
        "palabras_con_analisis": palabras_con_analisis,
        "frases_con_analisis": frases_con_analisis,
        "chats_procesados": len(vocabulario_data["metadata"]["chats_procesados"]),
        "ultima_actualizacion": vocabulario_data["metadata"]["ultima_actualizacion"],
        "top_palabras": top_palabras,
        "top_frases": top_frases,
        "top_categorias": top_categorias,
        "top_situaciones": top_situaciones,
        "modo_aprendizaje": modo_aprendizaje
    }

# Cargar vocabulario y base de conocimiento al iniciar el módulo
cargar_vocabulario()
cargar_base_conocimiento(config.BASE_CONOCIMIENTO_ARCHIVO)
