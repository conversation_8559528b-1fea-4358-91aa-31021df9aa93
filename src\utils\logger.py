"""
Módulo de logging para el bot de WhatsApp con Gemini.
Proporciona funciones para registrar eventos y errores en archivos de log.
"""

import os
import logging
from datetime import datetime
from logging.handlers import RotatingFileHandler
import pathlib

# Configuración de rutas
BASE_DIR = pathlib.Path(__file__).parent.parent.parent.absolute()
LOGS_DIR = os.path.join(BASE_DIR, "logs")

# Asegurarse de que el directorio de logs exista
os.makedirs(LOGS_DIR, exist_ok=True)

# Configuración de loggers
LOGGERS = {}

def get_logger(name, log_file=None, level=logging.INFO):
    """
    Obtiene o crea un logger con el nombre especificado.
    
    Args:
        name (str): Nombre del logger.
        log_file (str, opcional): Nombre del archivo de log. Si no se especifica,
                                 se usa el nombre del logger con extensión .log.
        level (int, opcional): Nivel de logging. Por defecto, INFO.
        
    Returns:
        logging.Logger: Logger configurado.
    """
    global LOGGERS
    
    # Si el logger ya existe, devolverlo
    if name in LOGGERS:
        return LOGGERS[name]
    
    # Crear un nuevo logger
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # Evitar duplicación de handlers
    if logger.handlers:
        return logger
    
    # Configurar el formato del log
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Añadir handler para consola
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # Añadir handler para archivo si se especifica
    if log_file is None:
        log_file = f"{name}.log"
    
    file_path = os.path.join(LOGS_DIR, log_file)
    file_handler = RotatingFileHandler(
        file_path, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # Guardar el logger para futuras referencias
    LOGGERS[name] = logger
    
    return logger

# Loggers predefinidos
bot_logger = get_logger("bot", "bot.log")
whatsapp_logger = get_logger("whatsapp", "whatsapp.log")
tester_logger = get_logger("tester", "tester.log")
vocabulary_logger = get_logger("vocabulary", "vocabulary.log")
story_logger = get_logger("story", "story.log")

# Función para registrar errores con más detalle
def log_error(logger, message, exception=None):
    """
    Registra un error con detalles adicionales.
    
    Args:
        logger (logging.Logger): Logger a utilizar.
        message (str): Mensaje de error.
        exception (Exception, opcional): Excepción que causó el error.
    """
    if exception:
        logger.error(f"{message}: {str(exception)}", exc_info=True)
    else:
        logger.error(message)

# Función para registrar el inicio de una operación
def log_start(logger, operation):
    """
    Registra el inicio de una operación.
    
    Args:
        logger (logging.Logger): Logger a utilizar.
        operation (str): Nombre de la operación.
    """
    logger.info(f"Iniciando: {operation}")

# Función para registrar el fin de una operación
def log_end(logger, operation, success=True):
    """
    Registra el fin de una operación.
    
    Args:
        logger (logging.Logger): Logger a utilizar.
        operation (str): Nombre de la operación.
        success (bool, opcional): Indica si la operación fue exitosa.
    """
    if success:
        logger.info(f"Completado con éxito: {operation}")
    else:
        logger.warning(f"Completado con errores: {operation}")

# Función para registrar una operación con emoji
def log_emoji(logger, emoji, message, level=logging.INFO):
    """
    Registra un mensaje con un emoji al principio.
    
    Args:
        logger (logging.Logger): Logger a utilizar.
        emoji (str): Emoji a incluir.
        message (str): Mensaje a registrar.
        level (int, opcional): Nivel de logging. Por defecto, INFO.
    """
    full_message = f"{emoji} {message}"
    
    if level == logging.DEBUG:
        logger.debug(full_message)
    elif level == logging.INFO:
        logger.info(full_message)
    elif level == logging.WARNING:
        logger.warning(full_message)
    elif level == logging.ERROR:
        logger.error(full_message)
    elif level == logging.CRITICAL:
        logger.critical(full_message)
