"""
Test para verificar el estilo de respuestas del bot.
Este script verifica que el bot no siempre incluya "?" al final de sus mensajes.
"""

import sys
import os
import unittest
import json
import random
from unittest.mock import patch, MagicMock

# Añadir el directorio raíz al path para poder importar los módulos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Importar el módulo central del bot
from src.bot.core import (
    inicializar_bot,
    procesar_mensaje_y_generar_respuesta
)

class TestEstiloRespuestas(unittest.TestCase):
    """Clase para probar el estilo de respuestas del bot."""

    def setUp(self):
        """Configuración inicial para las pruebas."""
        # Inicializar el bot con un archivo de historial temporal
        self.historial_archivo = "tests/historial_test_respuestas.json"
        inicializar_bot(self.historial_archivo)
        
        # ID de chat para la prueba
        self.chat_id = "test_respuestas"
        
        # Mensajes de prueba variados
        self.mensajes_prueba = [
            "Ho<PERSON>, ¿cómo estás?",
            "¿Qué has hecho hoy?",
            "Cuéntame algo interesante",
            "¿Te gusta la música?",
            "Tengo una idea para un proyecto",
            "¿Has visto la última película de Marvel?",
            "Estoy cansado",
            "¿Qué opinas sobre la inteligencia artificial?",
            "Me gustaría aprender a programar",
            "¿Tienes alguna recomendación de libros?"
        ]

    def tearDown(self):
        """Limpieza después de las pruebas."""
        # Eliminar el archivo de historial temporal si existe
        if os.path.exists(self.historial_archivo):
            os.remove(self.historial_archivo)

    @patch('google.generativeai.GenerativeModel')
    def test_variedad_respuestas(self, mock_generative_model):
        """
        Prueba que el bot no siempre incluya signos de interrogación al final de sus mensajes.
        También verifica que no use abreviaturas como 'q' en lugar de 'que'.
        """
        # Configurar el mock para devolver respuestas variadas
        mock_instance = MagicMock()
        mock_generative_model.return_value = mock_instance
        
        # Respuestas simuladas con y sin signos de interrogación
        respuestas_simuladas = [
            "Estoy bien, gracias por preguntar",
            "Hoy he estado trabajando en un proyecto interesante",
            "¿Te gustaría que te cuente sobre mi día?",
            "La música es una de mis pasiones, especialmente el jazz",
            "¿Qué tipo de proyecto tienes en mente?",
            "No la he visto aún, ¿es buena?",
            "Deberías descansar un poco entonces",
            "La IA es un campo fascinante con muchas posibilidades",
            "Programar es una habilidad muy útil, ¿por dónde te gustaría empezar?",
            "Te recomendaría 'El nombre del viento' de Patrick Rothfuss"
        ]
        
        # Configurar el mock para devolver las respuestas simuladas
        def side_effect(*args, **kwargs):
            response = MagicMock()
            response.text = random.choice(respuestas_simuladas)
            return response
        
        mock_instance.generate_content.side_effect = side_effect
        
        # Procesar varios mensajes y analizar las respuestas
        respuestas = []
        for mensaje in self.mensajes_prueba:
            respuesta = procesar_mensaje_y_generar_respuesta(mensaje, self.chat_id, self.historial_archivo)
            respuestas.append(respuesta)
        
        # Verificar que hay una mezcla de respuestas con y sin signos de interrogación
        respuestas_con_interrogacion = [r for r in respuestas if r and r.strip().endswith('?')]
        
        # Debe haber algunas respuestas sin signos de interrogación
        self.assertLess(len(respuestas_con_interrogacion), len(respuestas), 
                        "Todas las respuestas terminan con signo de interrogación")
        
        # Verificar que no se usan abreviaturas como 'q' en lugar de 'que'
        for respuesta in respuestas:
            if respuesta:
                palabras = respuesta.lower().split()
                # Verificar que no hay 'q' como palabra aislada
                self.assertNotIn('q', palabras, 
                                f"La respuesta '{respuesta}' contiene la abreviatura 'q'")
                
                # Verificar otras abreviaturas comunes
                self.assertNotIn('xq', palabras, 
                                f"La respuesta '{respuesta}' contiene la abreviatura 'xq'")
                self.assertNotIn('tmb', palabras, 
                                f"La respuesta '{respuesta}' contiene la abreviatura 'tmb'")

if __name__ == "__main__":
    unittest.main()
