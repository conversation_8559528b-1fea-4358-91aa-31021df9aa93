# WhatsApp Bot con Gemini

Bot de WhatsApp que utiliza la API de Google Gemini para generar respuestas conversacionales y naturales. El bot está diseñado para impersonar al usuario cuando no está disponible, manteniendo un estilo de comunicación natural y personalizado.

## Características

- **Integración con WhatsApp Web**: Funciona a través de WhatsApp Web usando Selenium
- **Modelo de lenguaje Gemini**: Utiliza la API de Google Gemini para generar respuestas naturales
- **Memoria de conversaciones**: Mantiene el contexto de las conversaciones por chat
- **Aprendizaje de vocabulario**: Aprende y utiliza el vocabulario específico de cada conversación
- **Información personal**: Utiliza información personal para personalizar las respuestas
- **Interfaz de prueba**: Incluye una interfaz web para probar el bot sin necesidad de WhatsApp
- **Capacidad de improvisación**: Puede inventar historias y anécdotas cuando se le pide
- **Sistema de temperatura de IA**: Permite ajustar la creatividad y libertad del bot en sus respuestas

## Estructura del proyecto

```
whatsapp-chatgpt-bot/
├── src/                      # Código fuente principal
│   ├── bot/                  # Lógica central del bot
│   │   ├── __init__.py
│   │   └── core.py           # Funcionalidad principal del bot
│   ├── config/               # Configuración
│   │   ├── __init__.py
│   │   └── settings.py       # Configuración global
│   ├── data/                 # Datos del bot
│   │   ├── base_conocimiento.json
│   │   ├── historial_conversaciones.json
│   │   ├── historial_conversaciones_test.json
│   │   ├── info_personal.json
│   │   └── vocabulario.json
│   ├── interfaces/           # Interfaces de usuario
│   │   ├── __init__.py
│   │   ├── tester.py         # Interfaz web de prueba
│   │   └── whatsapp.py       # Interfaz de WhatsApp
│   └── utils/                # Utilidades
│       ├── __init__.py
│       ├── logger.py         # Sistema de logging
│       ├── personal_info.py  # Gestión de información personal
│       ├── story_generator.py # Generador de historias
│       ├── temperature.py    # Sistema de temperatura de IA
│       ├── vocabulary.py     # Gestión de vocabulario
│       └── vocabulary_analyzer.py # Análisis de vocabulario
├── tests/                    # Pruebas unitarias
│   ├── __init__.py
│   ├── test_bot_improvisar.py # Prueba de improvisación
│   ├── test_respuestas.py    # Prueba de estilo de respuestas
│   ├── test_story_generator.py # Prueba del generador de historias
│   └── RunAllTests.bat       # Script para ejecutar todas las pruebas
├── scripts/                  # Scripts de inicio
│   ├── __init__.py
│   ├── IniciarBot.bat        # Iniciar bot de WhatsApp
│   ├── IniciarTester.bat     # Iniciar interfaz web
│   ├── IniciarTesterCLI.bat  # Iniciar interfaz CLI
│   └── run.bat               # Launcher unificado
├── templates/                # Plantillas HTML para la interfaz web
├── logs/                     # Archivos de log
├── chrome_profile/           # Perfil de Chrome para WhatsApp Web
├── .venv/                    # Entorno virtual (no incluido en el repositorio)
├── run.py                    # Script principal unificado
├── run_cli_tester.py         # Script para la interfaz CLI
├── run_tester.py             # Script para la interfaz web
└── run_whatsapp_bot.py       # Script para el bot de WhatsApp
```

## Requisitos

- Python 3.8 o superior
- Google Chrome instalado
- Cuenta de WhatsApp activa
- API Key de Google Gemini

## Instalación

1. Clonar el repositorio
2. Crear un entorno virtual:
   ```
   python -m venv .venv
   ```
3. Activar el entorno virtual:
   - Windows: `.venv\Scripts\activate`
   - Linux/Mac: `source .venv/bin/activate`
4. Instalar dependencias:
   ```
   pip install -r requirements.txt
   ```
5. Configurar la API Key de Gemini en `src/config/settings.py`
6. Personalizar la información personal en `src/data/info_personal.json`

## Uso

### Launcher Unificado

La forma más sencilla de iniciar el bot es usar el launcher unificado:

```
scripts\run.bat
```

Este script te permitirá elegir entre las diferentes interfaces disponibles.

### Bot de WhatsApp

Para iniciar el bot de WhatsApp directamente:

```
scripts\IniciarBot.bat
```

o

```
python run.py whatsapp
```

La primera vez que se ejecute, será necesario escanear el código QR con tu teléfono para iniciar sesión en WhatsApp Web.

### Interfaz de prueba

Hay varias opciones para probar el bot:

#### Interfaz web (requiere Flask)

Para iniciar la interfaz web de prueba:

```
scripts\IniciarTester.bat
```

o

```
python run.py web
```

Luego abre un navegador y ve a `http://localhost:5000` para acceder a la interfaz web de prueba.

#### Interfaz de línea de comandos (CLI)

Si prefieres una interfaz más simple:

```
scripts\IniciarTesterCLI.bat
```

o

```
python run.py cli
```

Esta interfaz no requiere Flask y es más simple de usar.

### Pruebas

Para ejecutar todas las pruebas:

```
tests\RunAllTests.bat
```

O ejecutar pruebas individuales:

```
python -m unittest tests.test_story_generator
python -m unittest tests.test_bot_improvisar
python -m unittest tests.test_respuestas
```

## Comandos del bot

El bot responde a los siguientes comandos en WhatsApp:

- `/ayuda` - Muestra la lista de comandos disponibles
- `/status` - Muestra el estado actual del bot
- `/borrar` - Borra el historial de la conversación actual
- `/silencio` - Activa el modo silencioso (no responde a mensajes)
- `/hablar` - Desactiva el modo silencioso
- `/debug` - Activa/desactiva el modo debug
- `/info` - Muestra información sobre el bot
- `/vocab` - Muestra estadísticas del vocabulario aprendido
- `/vocab_on` - Activa el aprendizaje de vocabulario
- `/vocab_off` - Desactiva el aprendizaje de vocabulario
- `/temp` - Muestra la temperatura actual de IA
- `/temp [0-100]` - Establece la temperatura de IA (0-100%)

## Personalización

### Información personal

Edita el archivo `src/data/info_personal.json` para personalizar la información que el bot utilizará para hacerse pasar por ti.

### Estilo de comunicación

El estilo de comunicación se puede ajustar modificando las instrucciones en `src/bot/core.py` en la variable `instrucciones_base`.

## Características avanzadas

### Sistema de temperatura de IA

El bot incluye un sistema de "temperatura" que permite ajustar cuánto se adhiere a su personalidad predefinida:

- **Temperatura baja (0%)**: El bot se adhiere estrictamente a su personalidad y estilo de comunicación predefinidos.
- **Temperatura alta (100%)**: El bot tiene más libertad creativa y puede explorar temas más diversos.

Esta funcionalidad está implementada en `src/utils/temperature.py` y se puede ajustar mediante:
- El comando `/temp [0-100]` en WhatsApp o en la interfaz CLI
- El control deslizante en la interfaz web de prueba

### Improvisación de historias

El bot puede inventar historias cuando menciona que se ha acordado de algo y el usuario le pregunta al respecto. Esta funcionalidad está implementada en `src/utils/story_generator.py`.

### Aprendizaje de vocabulario

El bot aprende el vocabulario utilizado en las conversaciones y lo utiliza para generar respuestas más naturales y personalizadas. Esta funcionalidad está implementada en `src/utils/vocabulary.py` y `src/utils/vocabulary_analyzer.py`.

## Logging

El sistema de logging guarda información detallada sobre la ejecución del bot en la carpeta `logs/`. Esto es útil para depurar problemas y monitorear el funcionamiento del bot.

## Contribuir

Si quieres contribuir a este proyecto, por favor:

1. Haz un fork del repositorio
2. Crea una rama para tu característica (`git checkout -b feature/nueva-caracteristica`)
3. Haz commit de tus cambios (`git commit -am 'Añadir nueva característica'`)
4. Haz push a la rama (`git push origin feature/nueva-caracteristica`)
5. Crea un Pull Request

## Licencia

Este proyecto está licenciado bajo la Licencia MIT - ver el archivo LICENSE para más detalles.
