"""
Interfaz de entrenamiento para el bot de WhatsApp con Gemini.
Esta interfaz permite entrenar al bot para que aprenda el estilo de comunicación del usuario.
"""

import os
import json
from datetime import datetime
from flask import Flask, request, jsonify, render_template
from flask_cors import CORS

# Importar el módulo central del bot
from src.bot.core import (
    inicializar_bot,
    procesar_mensaje_y_generar_respuesta,
    modo_silencioso,
    modo_debug
)

# Importar el analizador de estilo
from src.utils.style_analyzer import (
    analizar_estilo,
    guardar_perfil_estilo,
    cargar_perfil_estilo,
    aplicar_estilo_a_respuesta
)

# Importar configuración centralizada
from src.config import config

# Variables globales para seguimiento de tiempos de respuesta
ultimo_mensaje_timestamp = None
historial_entrenamiento = {}

def crear_app_entrenamiento():
    """Crea y configura la aplicación Flask para el entrenamiento."""
    # Configuración de la aplicación Flask
    app = Flask(__name__, template_folder=os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'templates'))
    app.config['SECRET_KEY'] = config.FLASK_SECRET_KEY

    # Configuración de CORS
    cors = CORS(app, resources={
        r"/*": {
            "origins": ["http://localhost:5001", "http://127.0.0.1:5001", "*"],
            "methods": ["GET", "POST", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization", "Accept"]
        }
    })

    # Añadir encabezados CORS a todas las respuestas
    @app.after_request
    def add_cors_headers(response):
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization,Accept')
        response.headers.add('Access-Control-Allow-Methods', 'GET,POST,OPTIONS')
        return response

    # Ruta principal - Interfaz de entrenamiento
    @app.route('/')
    def index():
        return render_template('trainer.html')

    # Ruta para enviar mensajes de entrenamiento
    @app.route('/api/train', methods=['POST'])
    def train():
        global ultimo_mensaje_timestamp
        
        data = request.json
        mensaje = data.get('mensaje', '').strip()
        chat_id = data.get('chat_id', 'training_session')
        
        if not mensaje:
            return jsonify({'error': 'Mensaje vacío'}), 400
        
        # Calcular tiempo de respuesta si hay un mensaje anterior
        tiempo_respuesta = None
        if ultimo_mensaje_timestamp:
            tiempo_respuesta = (datetime.now() - ultimo_mensaje_timestamp).total_seconds()
        
        # Actualizar timestamp
        ultimo_mensaje_timestamp = datetime.now()
        
        # Analizar el estilo del mensaje
        analisis = analizar_estilo(mensaje, chat_id, tiempo_respuesta)
        
        # Procesar el mensaje con el bot para obtener una respuesta
        respuesta_original = procesar_mensaje_y_generar_respuesta(mensaje, chat_id, config.HISTORIAL_TRAINING_ARCHIVO)
        
        # Aplicar estilo personal a la respuesta
        if isinstance(respuesta_original, list):
            respuesta_con_estilo = [aplicar_estilo_a_respuesta(r) for r in respuesta_original]
        else:
            respuesta_con_estilo = aplicar_estilo_a_respuesta(respuesta_original)
        
        # Devolver la respuesta y el análisis
        return jsonify({
            'respuesta_original': respuesta_original,
            'respuesta_con_estilo': respuesta_con_estilo,
            'analisis': analisis,
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        })
    
    # Ruta para obtener estadísticas del entrenamiento
    @app.route('/api/training_stats', methods=['GET'])
    def get_training_stats():
        perfil = cargar_perfil_estilo()
        
        # Preparar estadísticas resumidas
        stats = {
            "total_mensajes": perfil["metadata"]["total_mensajes"],
            "ultima_actualizacion": perfil["metadata"]["ultima_actualizacion"],
            "sesiones": len(perfil["metadata"]["sesiones"]),
            "estilo": {
                "capitaliza": f"{perfil['estilo']['capitalizacion']*100:.1f}%",
                "usa_puntuacion": f"{perfil['estilo']['puntuacion']*100:.1f}%",
                "usa_emojis": f"{perfil['estilo']['emojis']['frecuencia']*100:.1f}%",
                "longitud_promedio": f"{perfil['estilo']['longitud_mensajes']['promedio']:.1f} palabras"
            },
            "emojis_favoritos": [e["emoji"] for e in perfil["estilo"]["emojis"]["favoritos"][:5]],
            "palabras_frecuentes": sorted(perfil["vocabulario"]["palabras_frecuentes"].items(), key=lambda x: x[1], reverse=True)[:10],
            "frases_frecuentes": sorted(perfil["vocabulario"]["frases_frecuentes"].items(), key=lambda x: x[1], reverse=True)[:10],
            "patrones": {
                "saludos": len(perfil["patrones"]["saludos"]),
                "despedidas": len(perfil["patrones"]["despedidas"]),
                "afirmaciones": len(perfil["patrones"]["afirmaciones"]),
                "negaciones": len(perfil["patrones"]["negaciones"]),
                "preguntas": len(perfil["patrones"]["preguntas"])
            }
        }
        
        return jsonify(stats)
    
    # Ruta para reiniciar el entrenamiento
    @app.route('/api/reset_training', methods=['POST'])
    def reset_training():
        # Crear un perfil vacío
        perfil_vacio = {
            "metadata": {
                "ultima_actualizacion": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "total_mensajes": 0,
                "sesiones": []
            },
            "estilo": {
                "capitalizacion": 0.5,  # Probabilidad de capitalizar
                "puntuacion": 0.5,      # Probabilidad de usar puntuación
                "abreviaciones": {},    # Abreviaciones comunes
                "emojis": {
                    "frecuencia": 0.0,  # Frecuencia de uso de emojis
                    "favoritos": [],    # Emojis favoritos
                    "posicion": {       # Posición de los emojis
                        "inicio": 0.0,
                        "medio": 0.0,
                        "final": 0.0
                    }
                },
                "longitud_mensajes": {
                    "promedio": 0,
                    "desviacion": 0
                },
                "tiempo_respuesta": {
                    "promedio": 0,
                    "desviacion": 0
                }
            },
            "vocabulario": {
                "palabras_frecuentes": {},
                "frases_frecuentes": {}
            },
            "patrones": {
                "saludos": [],
                "despedidas": [],
                "afirmaciones": [],
                "negaciones": [],
                "preguntas": []
            }
        }
        
        # Guardar el perfil vacío
        guardar_perfil_estilo(perfil_vacio)
        
        # Reiniciar historial de entrenamiento
        if os.path.exists(config.HISTORIAL_TRAINING_ARCHIVO):
            try:
                os.remove(config.HISTORIAL_TRAINING_ARCHIVO)
                print(f"✅ Archivo de historial de entrenamiento eliminado: {config.HISTORIAL_TRAINING_ARCHIVO}")
            except Exception as e:
                print(f"❌ Error al eliminar archivo de historial: {e}")
        
        return jsonify({'status': 'success', 'message': 'Entrenamiento reiniciado'})
    
    # Ruta para exportar perfil de entrenamiento
    @app.route('/api/export_profile', methods=['GET'])
    def export_profile():
        perfil = cargar_perfil_estilo()
        return jsonify(perfil)
    
    # Ruta para importar perfil de entrenamiento
    @app.route('/api/import_profile', methods=['POST'])
    def import_profile():
        try:
            data = request.json
            if not data:
                return jsonify({'error': 'Datos de perfil vacíos'}), 400
            
            # Validar estructura básica
            if "metadata" not in data or "estilo" not in data or "vocabulario" not in data:
                return jsonify({'error': 'Estructura de perfil inválida'}), 400
            
            # Guardar perfil importado
            guardar_perfil_estilo(data)
            
            return jsonify({'status': 'success', 'message': 'Perfil importado correctamente'})
        except Exception as e:
            return jsonify({'error': f'Error al importar perfil: {str(e)}'}), 500
    
    return app

def cargar_historial_entrenamiento():
    """Carga el historial de entrenamiento desde el archivo JSON."""
    global historial_entrenamiento
    try:
        if os.path.exists(config.HISTORIAL_TRAINING_ARCHIVO):
            with open(config.HISTORIAL_TRAINING_ARCHIVO, 'r', encoding='utf-8') as archivo:
                historial_entrenamiento = json.load(archivo)
                print(f"✅ Historial de entrenamiento cargado")
    except Exception as e:
        print(f"❌ Error al cargar historial de entrenamiento: {e}")
        historial_entrenamiento = {}

def ejecutar_entrenamiento():
    """Función principal para ejecutar la interfaz de entrenamiento."""
    # Inicializar el bot con el módulo central
    inicializar_bot(config.HISTORIAL_TRAINING_ARCHIVO)
    
    # Cargar historial de entrenamiento
    cargar_historial_entrenamiento()
    
    print("🚀 Iniciando interfaz de entrenamiento del bot...")
    print("📋 Esta interfaz te permitirá entrenar al bot para que aprenda tu estilo de comunicación.")
    print(f"🌐 Servidor web disponible en: http://127.0.0.1:{config.PUERTO_SERVIDOR_TRAINING}")
    
    # Crear la aplicación Flask
    app = crear_app_entrenamiento()
    
    # Iniciar el servidor
    app.run(debug=config.FLASK_DEBUG, host=config.HOST_SERVIDOR_WEB, port=config.PUERTO_SERVIDOR_TRAINING)

if __name__ == '__main__':
    ejecutar_entrenamiento()
