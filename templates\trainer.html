<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Entrenamiento Personalizado</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #075e54;
            color: white;
            padding: 15px 0;
            text-align: center;
            margin-bottom: 20px;
            border-radius: 8px;
        }
        .chat-container {
            display: flex;
            height: calc(100vh - 200px);
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .chat-messages {
            flex: 2;
            padding: 20px;
            overflow-y: auto;
            background-color: #e5ddd5;
            background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23bbb' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
        }
        .analysis-panel {
            flex: 1;
            padding: 20px;
            background-color: #f8f9fa;
            border-left: 1px solid #ddd;
            overflow-y: auto;
        }
        .message {
            margin-bottom: 15px;
            max-width: 80%;
            padding: 10px 15px;
            border-radius: 15px;
            position: relative;
            word-wrap: break-word;
        }
        .user-message {
            background-color: #dcf8c6;
            margin-left: auto;
            border-top-right-radius: 0;
        }
        .bot-message {
            background-color: white;
            margin-right: auto;
            border-top-left-radius: 0;
        }
        .styled-message {
            background-color: #e3f2fd;
            margin-right: auto;
            border-top-left-radius: 0;
            border: 1px solid #90caf9;
        }
        .message-time {
            font-size: 0.7rem;
            color: #999;
            text-align: right;
            margin-top: 5px;
        }
        .input-area {
            display: flex;
            padding: 10px;
            background-color: white;
            border-top: 1px solid #ddd;
        }
        .input-area input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 20px;
            margin-right: 10px;
        }
        .input-area button {
            background-color: #075e54;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
        }
        .analysis-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #075e54;
        }
        .analysis-section {
            margin-bottom: 20px;
            padding: 10px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .analysis-item {
            margin-bottom: 5px;
            font-size: 0.9rem;
        }
        .analysis-label {
            font-weight: bold;
            color: #555;
        }
        .analysis-value {
            color: #333;
        }
        .stats-container {
            margin-top: 20px;
            padding: 15px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .stats-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #075e54;
        }
        .stats-section {
            margin-bottom: 15px;
        }
        .stats-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 0.9rem;
        }
        .stats-label {
            font-weight: bold;
            color: #555;
        }
        .stats-value {
            color: #333;
        }
        .action-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }
        .action-button {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.3s;
        }
        .reset-button {
            background-color: #f44336;
            color: white;
        }
        .export-button {
            background-color: #4caf50;
            color: white;
        }
        .import-button {
            background-color: #2196f3;
            color: white;
        }
        .emoji {
            font-size: 1.2rem;
            margin-right: 5px;
        }
        .word-cloud {
            margin-top: 15px;
            height: 150px;
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
            text-align: center;
        }
        .word {
            display: inline-block;
            margin: 5px;
            padding: 3px 6px;
            background-color: #e9ecef;
            border-radius: 4px;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Sistema de Entrenamiento Personalizado</h1>
            <p>Entrena al bot para que aprenda tu estilo de comunicación</p>
        </div>
        
        <div class="chat-container">
            <div class="chat-messages" id="chatMessages">
                <div class="message bot-message">
                    ¡Hola! Soy tu asistente de entrenamiento. Conversa conmigo para que pueda aprender tu estilo de comunicación.
                    <div class="message-time">Ahora</div>
                </div>
            </div>
            
            <div class="analysis-panel">
                <div class="analysis-title">Análisis en tiempo real</div>
                <div class="analysis-section" id="currentAnalysis">
                    <div class="analysis-item">
                        <span class="analysis-label">Esperando mensaje...</span>
                    </div>
                </div>
                
                <div class="stats-container">
                    <div class="stats-title">Estadísticas de entrenamiento</div>
                    <div class="stats-section" id="trainingStats">
                        <div class="stats-item">
                            <span class="stats-label">Total mensajes:</span>
                            <span class="stats-value" id="totalMessages">0</span>
                        </div>
                        <div class="stats-item">
                            <span class="stats-label">Última actualización:</span>
                            <span class="stats-value" id="lastUpdate">-</span>
                        </div>
                    </div>
                    
                    <div class="stats-title">Estilo detectado</div>
                    <div class="stats-section" id="styleStats">
                        <div class="stats-item">
                            <span class="stats-label">Capitalización:</span>
                            <span class="stats-value" id="capitalization">-</span>
                        </div>
                        <div class="stats-item">
                            <span class="stats-label">Uso de puntuación:</span>
                            <span class="stats-value" id="punctuation">-</span>
                        </div>
                        <div class="stats-item">
                            <span class="stats-label">Uso de emojis:</span>
                            <span class="stats-value" id="emojis">-</span>
                        </div>
                        <div class="stats-item">
                            <span class="stats-label">Longitud promedio:</span>
                            <span class="stats-value" id="avgLength">-</span>
                        </div>
                    </div>
                    
                    <div class="stats-title">Vocabulario frecuente</div>
                    <div class="word-cloud" id="wordCloud">
                        <!-- Las palabras se añadirán dinámicamente -->
                    </div>
                    
                    <div class="action-buttons">
                        <button class="action-button reset-button" id="resetButton">Reiniciar entrenamiento</button>
                        <button class="action-button export-button" id="exportButton">Exportar perfil</button>
                        <button class="action-button import-button" id="importButton">Importar perfil</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="input-area">
            <input type="text" id="messageInput" placeholder="Escribe un mensaje...">
            <button id="sendButton">➤</button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const chatMessages = document.getElementById('chatMessages');
            const messageInput = document.getElementById('messageInput');
            const sendButton = document.getElementById('sendButton');
            const currentAnalysis = document.getElementById('currentAnalysis');
            const resetButton = document.getElementById('resetButton');
            const exportButton = document.getElementById('exportButton');
            const importButton = document.getElementById('importButton');
            
            // Cargar estadísticas iniciales
            loadTrainingStats();
            
            // Función para enviar mensaje
            function sendMessage() {
                const message = messageInput.value.trim();
                if (message === '') return;
                
                // Añadir mensaje del usuario al chat
                addMessage('user', message);
                messageInput.value = '';
                
                // Enviar mensaje al servidor
                fetch('/api/train', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        mensaje: message,
                        chat_id: 'training_session'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    // Mostrar análisis
                    displayAnalysis(data.analisis);
                    
                    // Mostrar respuesta original del bot
                    if (Array.isArray(data.respuesta_original)) {
                        data.respuesta_original.forEach(resp => {
                            addMessage('bot', resp);
                        });
                    } else {
                        addMessage('bot', data.respuesta_original);
                    }
                    
                    // Mostrar respuesta con estilo aplicado
                    if (Array.isArray(data.respuesta_con_estilo)) {
                        data.respuesta_con_estilo.forEach(resp => {
                            addMessage('styled', resp);
                        });
                    } else {
                        addMessage('styled', data.respuesta_con_estilo);
                    }
                    
                    // Actualizar estadísticas
                    loadTrainingStats();
                })
                .catch(error => {
                    console.error('Error:', error);
                    addMessage('bot', 'Ha ocurrido un error al procesar tu mensaje.');
                });
            }
            
            // Función para añadir mensaje al chat
            function addMessage(type, text) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${type === 'user' ? 'user-message' : type === 'styled' ? 'styled-message' : 'bot-message'}`;
                messageDiv.textContent = text;
                
                const timeDiv = document.createElement('div');
                timeDiv.className = 'message-time';
                timeDiv.textContent = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                
                messageDiv.appendChild(timeDiv);
                chatMessages.appendChild(messageDiv);
                
                // Scroll al final
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
            
            // Función para mostrar análisis
            function displayAnalysis(analisis) {
                let html = '';
                
                if (analisis.capitalizacion !== undefined) {
                    html += `<div class="analysis-item">
                        <span class="analysis-label">Capitalización:</span>
                        <span class="analysis-value">${analisis.capitalizacion ? 'Sí' : 'No'}</span>
                    </div>`;
                }
                
                if (analisis.puntuacion) {
                    html += `<div class="analysis-item">
                        <span class="analysis-label">Puntuación:</span>
                        <span class="analysis-value">${analisis.puntuacion.usa_puntuacion ? 'Sí' : 'No'}</span>
                    </div>`;
                }
                
                if (analisis.emojis) {
                    html += `<div class="analysis-item">
                        <span class="analysis-label">Emojis:</span>
                        <span class="analysis-value">${analisis.emojis.usa_emojis ? 'Sí' : 'No'}</span>
                    </div>`;
                    
                    if (analisis.emojis.emojis_detectados && analisis.emojis.emojis_detectados.length > 0) {
                        html += `<div class="analysis-item">
                            <span class="analysis-label">Emojis detectados:</span>
                            <span class="analysis-value">${analisis.emojis.emojis_detectados.join(' ')}</span>
                        </div>`;
                    }
                }
                
                if (analisis.longitud) {
                    html += `<div class="analysis-item">
                        <span class="analysis-label">Longitud:</span>
                        <span class="analysis-value">${analisis.longitud.palabras} palabras, ${analisis.longitud.caracteres} caracteres</span>
                    </div>`;
                }
                
                if (analisis.tipo_mensaje) {
                    html += `<div class="analysis-item">
                        <span class="analysis-label">Tipo de mensaje:</span>
                        <span class="analysis-value">${analisis.tipo_mensaje}</span>
                    </div>`;
                }
                
                if (analisis.vocabulario && analisis.vocabulario.palabras_relevantes) {
                    html += `<div class="analysis-item">
                        <span class="analysis-label">Palabras relevantes:</span>
                        <span class="analysis-value">${analisis.vocabulario.palabras_relevantes.join(', ')}</span>
                    </div>`;
                }
                
                currentAnalysis.innerHTML = html;
            }
            
            // Función para cargar estadísticas de entrenamiento
            function loadTrainingStats() {
                fetch('/api/training_stats')
                .then(response => response.json())
                .then(data => {
                    // Actualizar estadísticas generales
                    document.getElementById('totalMessages').textContent = data.total_mensajes;
                    document.getElementById('lastUpdate').textContent = data.ultima_actualizacion;
                    
                    // Actualizar estadísticas de estilo
                    if (data.estilo) {
                        document.getElementById('capitalization').textContent = data.estilo.capitaliza;
                        document.getElementById('punctuation').textContent = data.estilo.usa_puntuacion;
                        document.getElementById('emojis').textContent = data.estilo.usa_emojis;
                        document.getElementById('avgLength').textContent = data.estilo.longitud_promedio;
                    }
                    
                    // Actualizar nube de palabras
                    const wordCloud = document.getElementById('wordCloud');
                    wordCloud.innerHTML = '';
                    
                    if (data.palabras_frecuentes && data.palabras_frecuentes.length > 0) {
                        data.palabras_frecuentes.forEach(([palabra, frecuencia]) => {
                            const wordSpan = document.createElement('span');
                            wordSpan.className = 'word';
                            wordSpan.textContent = palabra;
                            wordSpan.style.fontSize = `${0.8 + (frecuencia / 5) * 0.2}rem`;
                            wordCloud.appendChild(wordSpan);
                        });
                    } else {
                        wordCloud.textContent = 'No hay suficientes datos para mostrar palabras frecuentes.';
                    }
                })
                .catch(error => {
                    console.error('Error al cargar estadísticas:', error);
                });
            }
            
            // Evento para enviar mensaje con el botón
            sendButton.addEventListener('click', sendMessage);
            
            // Evento para enviar mensaje con Enter
            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
            
            // Evento para reiniciar entrenamiento
            resetButton.addEventListener('click', function() {
                if (confirm('¿Estás seguro de que quieres reiniciar el entrenamiento? Se perderán todos los datos recopilados.')) {
                    fetch('/api/reset_training', {
                        method: 'POST'
                    })
                    .then(response => response.json())
                    .then(data => {
                        alert('Entrenamiento reiniciado correctamente.');
                        loadTrainingStats();
                    })
                    .catch(error => {
                        console.error('Error al reiniciar entrenamiento:', error);
                        alert('Error al reiniciar entrenamiento.');
                    });
                }
            });
            
            // Evento para exportar perfil
            exportButton.addEventListener('click', function() {
                fetch('/api/export_profile')
                .then(response => response.json())
                .then(data => {
                    const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(data, null, 2));
                    const downloadAnchorNode = document.createElement('a');
                    downloadAnchorNode.setAttribute("href", dataStr);
                    downloadAnchorNode.setAttribute("download", "perfil_estilo.json");
                    document.body.appendChild(downloadAnchorNode);
                    downloadAnchorNode.click();
                    downloadAnchorNode.remove();
                })
                .catch(error => {
                    console.error('Error al exportar perfil:', error);
                    alert('Error al exportar perfil.');
                });
            });
            
            // Evento para importar perfil
            importButton.addEventListener('click', function() {
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = '.json';
                
                input.onchange = e => {
                    const file = e.target.files[0];
                    const reader = new FileReader();
                    
                    reader.onload = function(event) {
                        try {
                            const perfil = JSON.parse(event.target.result);
                            
                            fetch('/api/import_profile', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify(perfil)
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.status === 'success') {
                                    alert('Perfil importado correctamente.');
                                    loadTrainingStats();
                                } else {
                                    alert('Error al importar perfil: ' + data.error);
                                }
                            })
                            .catch(error => {
                                console.error('Error al importar perfil:', error);
                                alert('Error al importar perfil.');
                            });
                        } catch (error) {
                            console.error('Error al parsear archivo JSON:', error);
                            alert('El archivo seleccionado no es un JSON válido.');
                        }
                    };
                    
                    reader.readAsText(file);
                };
                
                input.click();
            });
        });
    </script>
</body>
</html>
