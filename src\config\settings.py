"""
Configuración global para el bot de WhatsApp.
"""

import os
import pathlib

# Rutas de archivos
BASE_DIR = pathlib.Path(__file__).parent.parent.parent.absolute()
DATA_DIR = os.path.join(BASE_DIR, "src", "data")

# Asegurarse de que el directorio de datos exista
os.makedirs(DATA_DIR, exist_ok=True)

# Archivos de datos
HISTORIAL_ARCHIVO = os.path.join(DATA_DIR, "historial_conversaciones.json")
HISTORIAL_TEST_ARCHIVO = os.path.join(DATA_DIR, "historial_conversaciones_test.json")
INFO_PERSONAL_ARCHIVO = os.path.join(DATA_DIR, "info_personal.json")
VOCABULARIO_ARCHIVO = os.path.join(DATA_DIR, "vocabulario.json")
BASE_CONOCIMIENTO_ARCHIVO = os.path.join(DATA_DIR, "base_conocimiento.json")

# Configuración de Chrome para WhatsApp Web
USER_DATA_DIR = os.path.join(BASE_DIR, "chrome_profile")

# Configuración de la API de Gemini
GEMINI_API_KEY = "AIzaSyDjtn7D8kDhvqt44mqJk0Awev0aCv7DYt0"
GEMINI_MODEL = "gemini-1.5-pro"

# Configuración del bot
MAX_MENSAJES_HISTORIAL = 50
